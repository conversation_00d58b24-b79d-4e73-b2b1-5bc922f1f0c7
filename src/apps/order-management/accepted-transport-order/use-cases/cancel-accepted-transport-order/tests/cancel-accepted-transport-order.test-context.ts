import { <PERSON><PERSON>ty<PERSON>anager } from 'typeorm'
import { ShiftEntityBuilder } from '../../../../../resource-management/shift/builders/shift.entity.builder.js'
import { Shift } from '../../../../../resource-management/shift/entities/shift.entity.js'
import { Branch } from '../../../../../resource-management/branch/branch.entity.js'
import { BranchEntityBuilder } from '../../../../../resource-management/branch/builders/branch.entity.builder.js'
import { DriverEntityBuilder } from '../../../../../resource-management/driver/entities/driver/driver.entity.builder.js'
import { Driver } from '../../../../../resource-management/driver/entities/driver/driver.entity.js'
import { ContractTypeEntityBuilder } from '../../../../contract-type/builders/contract-type.entity.builder.js'
import { CareUserEntityBuilder } from '../../../../care-user/builders/care-user.entity.builder.js'
import { CareUser } from '../../../../care-user/entities/care-user.entity.js'
import { AcceptedTransportOrderEntityBuilder } from '../../../builders/accepted-transport-order.entity.builder.js'
import { AcceptedTransportOrder } from '../../../entities/accepted-transport-order.entity.js'
import { ContractType } from '../../../../contract-type/entities/contract-type.entity.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { MaxTimeInVehicleFormula } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { MaxTimeInVehicleFormulaEntityBuilder } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { ClientId } from '../../../../client/client-id.js'
import { ClientType } from '../../../../client/client-type.js'
import { DriverDefaultShift } from '../../../../../resource-management/driver-default-shift/entities/driver-default-shift.entity.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../../resource-management/driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { ContractEntityBuilder } from '../../../../contract/entities/contract.entity.builder.js'
import { Contract } from '../../../../contract/entities/contract.entity.js'
import { UserEntityBuilder } from '../../../../../../app/users/tests/user-entity.builder.js'
import { User } from '../../../../../../app/users/entities/user.entity.js'
import { PricingFormula } from '../../../../../pricing/pricing-formula/entities/pricing-formula.entity.js'
import { PricingFormulaEntityBuilder } from '../../../../../pricing/pricing-formula/pricing-formula.entity.builder.js'

export interface CancelAcceptedTransportOrdersTestContext {
  order: AcceptedTransportOrder
}

export async function setupCancelAcceptedTransportOrdersTestContext (
  entityManager: EntityManager
): Promise<CancelAcceptedTransportOrdersTestContext> {
  const user = new UserEntityBuilder().build()

  const location = new LocationBuilder().build()

  const branch = new BranchEntityBuilder()
    .build()

  const defaultShift = new DriverDefaultShiftEntityBuilder()
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const driver = new DriverEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withFirstName('aa')
    .withLastName('aa')
    .withDefaultShiftUuid(defaultShift.uuid)
    .build()

  const shift = new ShiftEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withDriverUuid(driver.uuid)
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const timeInVehicleFormula = new MaxTimeInVehicleFormulaEntityBuilder().build()
  await entityManager.insert(MaxTimeInVehicleFormula, timeInVehicleFormula)

  const pricingFormula = new PricingFormulaEntityBuilder().build()
  await entityManager.insert(PricingFormula, pricingFormula)

  const contractType = new ContractTypeEntityBuilder()
    .withMaxTimeInVehicleFormulaUuid(timeInVehicleFormula.uuid)
    .withPricingFormulaUuid(pricingFormula.uuid)
    .build()

  const careUser = new CareUserEntityBuilder().build()

  const contract = new ContractEntityBuilder()
    .withContractTypeUuid(contractType.uuid)
    .withClientId(new ClientId(careUser.uuid, ClientType.CARE_USER))
    .build()

  const order = new AcceptedTransportOrderEntityBuilder()
    .withClientId(new ClientId(careUser.uuid, ClientType.CARE_USER))
    .withCareUserUuid(careUser.uuid)
    .withContractUuid(contract.uuid)
    .withPickupLocationUuid(location.uuid)
    .withDropOffLocationUuid(location.uuid)
    .withFormulaUuid(timeInVehicleFormula.uuid)
    .build()

  await entityManager.insert(User, user)
  await entityManager.insert(Branch, branch)
  await entityManager.insert(Location, location)
  await entityManager.insert(DriverDefaultShift, defaultShift)
  await entityManager.insert(Driver, driver)
  await entityManager.insert(Shift, shift)
  await entityManager.insert(ContractType, contractType)
  await entityManager.insert(CareUser, careUser)
  await entityManager.insert(Contract, contract)
  await entityManager.insert(AcceptedTransportOrder, order)

  return { order }
}
