import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { AcceptedTransportOrder } from '../../entities/accepted-transport-order.entity.js'
import {
  AcceptedTransportOrderPreferredDriver
} from '../../entities/accepted-transport-order-preferred-driver.entity.js'
import { Location } from '../../../../../modules/location/entities/location.entity.js'
import { Organization } from '../../../organization/entities/organization.entity.js'
import { CareUser } from '../../../care-user/entities/care-user.entity.js'
import {
  MaxTimeInVehicleFormula
} from '../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { Driver } from '../../../../resource-management/driver/entities/driver/driver.entity.js'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { ClientModule } from '../../../client/client-module.js'
import { Contract } from '../../../contract/entities/contract.entity.js'
import { CreateAcceptedTransportOrderUseCase } from './create-accepted-transport-order.use-case.js'
import { CreateAcceptedTransportOrderController } from './create-accepted-transport-order.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AcceptedTransportOrder,
      AcceptedTransportOrderPreferredDriver,
      Location,
      Organization,
      CareUser,
      Contract,
      MaxTimeInVehicleFormula,
      Driver
    ]),
    DomainEventEmitterModule,
    ClientModule
  ],
  providers: [
    CreateAcceptedTransportOrderUseCase
  ],
  controllers: [
    CreateAcceptedTransportOrderController
  ]
})
export class CreateAcceptedTransportOrderModule {}
