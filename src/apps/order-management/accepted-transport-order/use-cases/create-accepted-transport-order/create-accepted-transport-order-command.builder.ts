import { randomUUID } from 'crypto'
import dayjs from 'dayjs'
import { TargetAction } from '../../enums/target-action.enum.js'
import { DateFormats } from '../../../../../utils/dates/date-formats.js'
import { SeatsDemandCommand } from '../../../seats-demand/create-seat-demand.command.js'
import { OrderTripType } from '../../enums/order-trip-type.enum.js'
import { ClientIdCommand } from '../../../client/client-id.command.js'
import { ClientIdCommandBuilder } from '../../../client/client-id.command.builder.js'
import { generateLocationUuid, LocationUuid } from '../../../../../modules/location/entities/location.uuid.js'
import { DriverUuid } from '../../../../resource-management/driver/entities/driver/driver.uuid.js'
import { CreateAcceptedTransportOrderCommand } from './create-accepted-transport-order.command.js'

export class CreateAcceptedTransportOrderCommandBuilder {
  private command: CreateAcceptedTransportOrderCommand

  constructor () {
    this.command = new CreateAcceptedTransportOrderCommand()
    this.command.id = randomUUID()
    this.command.date = dayjs().format(DateFormats.POSTGRES_DATE)
    this.command.description = null
    this.command.targetAction = TargetAction.PICKUP
    this.command.targetTime = new Date().toISOString()
    this.command.arrivalWindowFrom = new Date().toISOString()
    this.command.arrivalWindowUntil = new Date().toISOString()
    this.command.seatsDemand = {
      childSeats: 0,
      normalSeats: 1,
      wheelChairSeats: 0
    }
    this.command.pickupServiceTimeInSeconds = 0
    this.command.dropOffServiceTimeInSeconds = 0
    this.command.tripType = OrderTripType.OUTBOUND
    this.command.dropOffLocationUuid = generateLocationUuid()
    this.command.pickupLocationUuid = generateLocationUuid()
    this.command.careUserUuid = randomUUID()
    this.command.maxTimeInVehicleFormulaUuid = randomUUID()
    this.command.clientId = new ClientIdCommandBuilder().build()
    this.command.preferredDriverUuids = []
  }

  withId (id: string): this {
    this.command.id = id
    return this
  }

  withDate (date: string): this {
    this.command.date = date
    return this
  }

  withDescription (description: string | null): this {
    this.command.description = description
    return this
  }

  withTargetAction (targetAction: TargetAction): this {
    this.command.targetAction = targetAction
    return this
  }

  withTargetTime (targetTime: string): this {
    this.command.targetTime = targetTime
    return this
  }

  withArrivalWindowFrom (arrivalWindowFrom: string): this {
    this.command.arrivalWindowFrom = arrivalWindowFrom
    return this
  }

  withArrivalWindowUntil (arrivalWindowUntil: string): this {
    this.command.arrivalWindowUntil = arrivalWindowUntil
    return this
  }

  withSeatsDemand (seatsDemandCommand: SeatsDemandCommand): this {
    this.command.seatsDemand = seatsDemandCommand
    return this
  }

  withPickupLocationUuid (pickupLocationUuid: LocationUuid): this {
    this.command.pickupLocationUuid = pickupLocationUuid
    return this
  }

  withDropOffLocationUuid (dropOffLocationUuid: LocationUuid): this {
    this.command.dropOffLocationUuid = dropOffLocationUuid
    return this
  }

  withPickupServiceTimeInSeconds (pickupServiceTimeInSeconds: number): this {
    this.command.pickupServiceTimeInSeconds = pickupServiceTimeInSeconds
    return this
  }

  withDropOffServiceTimeInSeconds (dropOffServiceTimeInSeconds: number): this {
    this.command.dropOffServiceTimeInSeconds = dropOffServiceTimeInSeconds
    return this
  }

  withClientId (clientId: ClientIdCommand): this {
    this.command.clientId = clientId
    return this
  }

  withCareUserUuid (careUserUuid: string): this {
    this.command.careUserUuid = careUserUuid
    return this
  }

  withContractUuid (contractUuid: string): this {
    this.command.contractUuid = contractUuid
    return this
  }

  withPreferredDriverUuids (preferredDriverUuids: DriverUuid[]): this {
    this.command.preferredDriverUuids = preferredDriverUuids
    return this
  }

  withMaxTimeInVehicleFormulaUuid (maxTimeInVehicleFormulaUuid: string): this {
    this.command.maxTimeInVehicleFormulaUuid = maxTimeInVehicleFormulaUuid
    return this
  }

  withTripType (tripType: OrderTripType): this {
    this.command.tripType = tripType
    return this
  }

  build (): CreateAcceptedTransportOrderCommand {
    return this.command
  }
}
