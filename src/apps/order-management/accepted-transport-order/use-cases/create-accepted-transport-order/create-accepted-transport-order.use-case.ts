import { randomUUID } from 'node:crypto'
import { Injectable } from '@nestjs/common'
import { Any, DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { AcceptedTransportOrder } from '../../entities/accepted-transport-order.entity.js'
import { Duration } from '../../../../../utils/quantities/duration.js'
import { AcceptedTransportOrderPreferredDriver } from '../../entities/accepted-transport-order-preferred-driver.entity.js'
import { CareUser } from '../../../care-user/entities/care-user.entity.js'
import { Driver } from '../../../../resource-management/driver/entities/driver/driver.entity.js'
import { LocationNotFoundApiError } from '../../../../../modules/location/location-not-found.api-error.js'
import { DriverNotFoundError } from '../../../../resource-management/driver/errors/driver-not-found.error.js'
import { MaxTimeInVehicleFormula } from '../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { Location } from '../../../../../modules/location/entities/location.entity.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { ClientRepository } from '../../../client/client-repository.js'
import { Contract } from '../../../contract/entities/contract.entity.js'
import { CreateAcceptedTransportOrderCommand } from './create-accepted-transport-order.command.js'
import { CreateAcceptedTransportOrderResponse } from './create-accepted-transport-order.response.js'
import { AcceptedTransportOrderCreatedEvent } from './accepted-transport-order-created.event.js'

@Injectable()
export class CreateAcceptedTransportOrderUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(AcceptedTransportOrder)
    private readonly orderRepo: Repository<AcceptedTransportOrder>,
    @InjectRepository(AcceptedTransportOrderPreferredDriver)
    private readonly preferredDriverRepo: Repository<AcceptedTransportOrderPreferredDriver>,
    @InjectRepository(Location)
    private readonly locationRepo: Repository<Location>,
    @InjectRepository(CareUser)
    private readonly careUserRepo: Repository<CareUser>,
    @InjectRepository(Contract)
    private readonly contractRepo: Repository<Contract>,
    @InjectRepository(MaxTimeInVehicleFormula)
    private readonly maxTimeInVehicleFormulaRepo: Repository<MaxTimeInVehicleFormula>,
    @InjectRepository(Driver)
    private readonly driverRepo: Repository<Driver>,
    private readonly clientRepo: ClientRepository
  ) {}

  async execute (
    command: CreateAcceptedTransportOrderCommand
  ): Promise<CreateAcceptedTransportOrderResponse> {
    await this.validateUuids(command)
    const acceptedTransportOrder = this.createAcceptedTransportOrder(command)

    const preferredDrivers = this.createPreferredDrivers(
      acceptedTransportOrder.uuid,
      command.preferredDriverUuids
    )
    const event = new AcceptedTransportOrderCreatedEvent(acceptedTransportOrder.uuid)

    await transaction(this.dataSource, async () => {
      await this.orderRepo.insert(acceptedTransportOrder)
      await this.preferredDriverRepo.insert(preferredDrivers)
      await this.eventEmitter.emitOne(event)
    })

    return new CreateAcceptedTransportOrderResponse(acceptedTransportOrder)
  }

  private async validateUuids (command: CreateAcceptedTransportOrderCommand): Promise<void> {
    const [locations, drivers, ..._] = await Promise.all([
      this.locationRepo.findBy(
        { uuid: Any([command.pickupLocationUuid, command.dropOffLocationUuid]) }
      ),
      this.driverRepo.findBy({ uuid: Any(command.preferredDriverUuids) }),
      this.clientRepo.findByIdOrFail(command.clientId.toId()),
      this.careUserRepo.findOneByOrFail({ uuid: command.careUserUuid }),
      this.contractRepo.findOneByOrFail({ uuid: command.contractUuid }),
      this.maxTimeInVehicleFormulaRepo.findOneByOrFail(
        { uuid: command.maxTimeInVehicleFormulaUuid }
      )
    ])

    const pickupLocationExists = locations.some(l => l.uuid === command.pickupLocationUuid)
    const dropOffLocationExists = locations.some(l => l.uuid === command.dropOffLocationUuid)

    if (!pickupLocationExists) {
      throw new LocationNotFoundApiError(command.pickupLocationUuid)
    }

    if (!dropOffLocationExists) {
      throw new LocationNotFoundApiError(command.dropOffLocationUuid)
    }

    if (drivers.length < command.preferredDriverUuids.length) {
      throw new DriverNotFoundError()
    }
  }

  private createAcceptedTransportOrder (command: CreateAcceptedTransportOrderCommand) {
    return this.orderRepo.create({
      uuid: randomUUID(),
      id: command.id,
      date: command.date,
      description: command.description,
      targetAction: command.targetAction,
      targetTime: command.targetTime,
      arrivalWindowFrom: command.arrivalWindowFrom,
      arrivalWindowUntil: command.arrivalWindowUntil,
      seatsDemand: command.seatsDemand,
      pickupLocationUuid: command.pickupLocationUuid,
      pickupServiceTime: new Duration(command.pickupServiceTimeInSeconds, 's'),
      dropOffLocationUuid: command.dropOffLocationUuid,
      dropOffServiceTime: new Duration(command.dropOffServiceTimeInSeconds, 's'),
      clientId: command.clientId,
      careUserUuid: command.careUserUuid,
      contractUuid: command.contractUuid,
      maxTimeInVehicleFormulaUuid: command.maxTimeInVehicleFormulaUuid,
      tripType: command.tripType
    })
  }

  private createPreferredDrivers (
    transportOrderUuid: string,
    preferredDriverUuids: string[]
  ): AcceptedTransportOrderPreferredDriver[] {
    return preferredDriverUuids.map((uuid) => {
      return this.preferredDriverRepo.create({
        transportOrderUuid,
        driverUuid: uuid
      })
    })
  }
}
