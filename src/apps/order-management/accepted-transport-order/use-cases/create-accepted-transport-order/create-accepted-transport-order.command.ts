import { ApiProperty } from '@nestjs/swagger'
import {
  ArrayUnique,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsString,
  IsUUID,
  ValidateNested
} from 'class-validator'
import { Type } from 'class-transformer'
import { IsDateWithoutTimeString } from '../../../../../utils/validators/is-date-without-time-string.validator.js'
import { IsNullable } from '../../../../../utils/validators/is-nullable.validator.js'
import { TargetAction, TargetActionApiProperty } from '../../enums/target-action.enum.js'
import { DateFormats } from '../../../../../utils/dates/date-formats.js'
import { SeatsDemandCommand } from '../../../seats-demand/create-seat-demand.command.js'
import { OrderTripType, OrderTripTypeApiProperty } from '../../enums/order-trip-type.enum.js'
import { ClientIdCommand } from '../../../client/client-id.command.js'
import { LocationUuid } from '../../../../../modules/location/entities/location.uuid.js'
import { DriverUuid } from '../../../../resource-management/driver/entities/driver/driver.uuid.js'

export class CreateAcceptedTransportOrderCommand {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  id: string

  @ApiProperty({ type: String, format: 'date' })
  @IsDateWithoutTimeString()
  date: string

  @ApiProperty({ type: String, format: 'string', nullable: true })
  @IsString()
  @IsNullable()
  description: string | null

  @TargetActionApiProperty()
  @IsEnum(TargetAction)
  targetAction: TargetAction

  @ApiProperty({
    description: 'The target time of the order',
    example: DateFormats.ISO_8601,
    type: 'string',
    format: 'date-time'
  })
  @IsDateString({ strict: true })
  targetTime: string

  @ApiProperty({
    description: 'The arrival window from of the order',
    example: DateFormats.ISO_8601,
    type: 'string',
    format: 'date-time'
  })
  @IsDateString({ strict: true })
  arrivalWindowFrom: string

  @IsDateString({ strict: true })
  @ApiProperty({
    description: 'The arrival window until of the order',
    example: DateFormats.ISO_8601,
    type: 'string',
    format: 'date-time'
  })
  @IsDateString({ strict: true })
  arrivalWindowUntil: string

  @ApiProperty({ type: SeatsDemandCommand })
  @ValidateNested()
  @Type(() => SeatsDemandCommand)
  @IsNotEmpty()
  seatsDemand: SeatsDemandCommand

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  pickupLocationUuid: LocationUuid

  @ApiProperty({ type: Number })
  @IsNumber()
  pickupServiceTimeInSeconds: number

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  dropOffLocationUuid: LocationUuid

  @ApiProperty({ type: Number })
  @IsNumber()
  dropOffServiceTimeInSeconds: number

  @ApiProperty({ type: ClientIdCommand })
  @IsObject()
  @ValidateNested()
  @Type(() => ClientIdCommand)
  clientId: ClientIdCommand

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  careUserUuid: string

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  contractUuid: string

  @ApiProperty({ type: String, format: 'uuid', isArray: true })
  @ArrayUnique()
  @IsUUID(undefined, { each: true })
  preferredDriverUuids: DriverUuid[]

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  maxTimeInVehicleFormulaUuid: string

  @OrderTripTypeApiProperty()
  @IsEnum(OrderTripType)
  tripType: OrderTripType
}
