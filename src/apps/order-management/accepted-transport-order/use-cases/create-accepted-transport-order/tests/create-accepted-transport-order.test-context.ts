import { <PERSON><PERSON>tyManager } from 'typeorm'
import { Branch } from '../../../../../resource-management/branch/branch.entity.js'
import { BranchEntityBuilder } from '../../../../../resource-management/branch/builders/branch.entity.builder.js'
import { DriverEntityBuilder } from '../../../../../resource-management/driver/entities/driver/driver.entity.builder.js'
import { Driver } from '../../../../../resource-management/driver/entities/driver/driver.entity.js'
import { OrganizationEntityBuilder } from '../../../../organization/builders/organization.entity.builder.js'
import { ContractTypeEntityBuilder } from '../../../../contract-type/builders/contract-type.entity.builder.js'
import { CareUserEntityBuilder } from '../../../../care-user/builders/care-user.entity.builder.js'
import { CareUser } from '../../../../care-user/entities/care-user.entity.js'
import { Organization } from '../../../../organization/entities/organization.entity.js'
import { ContractType } from '../../../../contract-type/entities/contract-type.entity.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { MaxTimeInVehicleFormula } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { MaxTimeInVehicleFormulaEntityBuilder } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../../resource-management/driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../../resource-management/driver-default-shift/entities/driver-default-shift.entity.js'
import { Contract } from '../../../../contract/entities/contract.entity.js'
import { ClientId } from '../../../../client/client-id.js'
import { ClientType } from '../../../../client/client-type.js'
import { ContractEntityBuilder } from '../../../../contract/entities/contract.entity.builder.js'
import { UserEntityBuilder } from '../../../../../../app/users/tests/user-entity.builder.js'
import { User } from '../../../../../../app/users/entities/user.entity.js'
import { PricingFormula } from '../../../../../pricing/pricing-formula/entities/pricing-formula.entity.js'
import { PricingFormulaEntityBuilder } from '../../../../../pricing/pricing-formula/pricing-formula.entity.builder.js'

export interface CreateAcceptedTransportOrdersTestContext {
  location: Location
  organization: Organization
  careUser: CareUser
  contract: Contract
  drivers: Driver[]
  maxTimeInVehicleFormula: MaxTimeInVehicleFormula
}

export async function setupUpdateAcceptedTransportOrdersTestContext (
  entityManager: EntityManager
): Promise<CreateAcceptedTransportOrdersTestContext> {
  const user = new UserEntityBuilder().build()

  const location = new LocationBuilder().build()

  const branch = new BranchEntityBuilder()
    .build()

  const defaultShift1 = new DriverDefaultShiftEntityBuilder()
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const driver1 = new DriverEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withFirstName('aa')
    .withLastName('aa')
    .withDefaultShiftUuid(defaultShift1.uuid)
    .build()

  const defaultShift2 = new DriverDefaultShiftEntityBuilder()
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const driver2 = new DriverEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withFirstName('bb')
    .withLastName('bb')
    .withDefaultShiftUuid(defaultShift2.uuid)
    .build()

  const defaultShift3 = new DriverDefaultShiftEntityBuilder()
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const driver3 = new DriverEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withFirstName('cc')
    .withLastName('cc')
    .withDefaultShiftUuid(defaultShift3.uuid)
    .build()

  const timeInVehicleFormula = new MaxTimeInVehicleFormulaEntityBuilder()
    .build()
  await entityManager.insert(MaxTimeInVehicleFormula, timeInVehicleFormula)

  const pricingFormula = new PricingFormulaEntityBuilder().build()
  await entityManager.insert(PricingFormula, pricingFormula)

  const contractType = new ContractTypeEntityBuilder()
    .withMaxTimeInVehicleFormulaUuid(timeInVehicleFormula.uuid)
    .withPricingFormulaUuid(pricingFormula.uuid)
    .build()

  const organization = new OrganizationEntityBuilder()
    .build()

  const careUser = new CareUserEntityBuilder()
    .build()

  const contract = new ContractEntityBuilder()
    .withContractTypeUuid(contractType.uuid)
    .withClientId(new ClientId(careUser.uuid, ClientType.CARE_USER))
    .build()

  await entityManager.insert(User, user)
  await entityManager.insert(Branch, branch)
  await entityManager.insert(Location, location)
  await entityManager.insert(DriverDefaultShift, [defaultShift1, defaultShift2, defaultShift3])
  await entityManager.insert(Driver, [driver1, driver2, driver3])
  await entityManager.insert(ContractType, contractType)
  await entityManager.insert(Organization, organization)
  await entityManager.insert(CareUser, careUser)
  await entityManager.insert(Contract, contract)

  return {
    location: location,
    organization: organization,
    careUser: careUser,
    contract,
    drivers: [driver1, driver2, driver3],
    maxTimeInVehicleFormula: timeInVehicleFormula
  }
}
