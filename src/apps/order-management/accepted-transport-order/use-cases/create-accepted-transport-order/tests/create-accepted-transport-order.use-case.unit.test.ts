import { before, describe, it } from 'node:test'
import { createStubInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { CreateAcceptedTransportOrderUseCase } from '../create-accepted-transport-order.use-case.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { AcceptedTransportOrder } from '../../../entities/accepted-transport-order.entity.js'
import {
  AcceptedTransportOrderPreferredDriver
} from '../../../entities/accepted-transport-order-preferred-driver.entity.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { CareUser } from '../../../../care-user/entities/care-user.entity.js'
import { ContractType } from '../../../../contract-type/entities/contract-type.entity.js'
import {
  MaxTimeInVehicleFormula
} from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { Driver } from '../../../../../resource-management/driver/entities/driver/driver.entity.js'
import {
  CreateAcceptedTransportOrderCommandBuilder
} from '../create-accepted-transport-order-command.builder.js'
import {
  AcceptedTransportOrderCreatedEvent
} from '../accepted-transport-order-created.event.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { CareUserEntityBuilder } from '../../../../care-user/builders/care-user.entity.builder.js'
import {
  ContractTypeEntityBuilder
} from '../../../../contract-type/builders/contract-type.entity.builder.js'
import {
  MaxTimeInVehicleFormulaEntityBuilder
} from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.builder.js'
import {
  AcceptedTransportOrderEntityBuilder
} from '../../../builders/accepted-transport-order.entity.builder.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { ClientRepository } from '../../../../client/client-repository.js'

describe('CreateAcceptedTransportOrderUseCase - Unit Tests', () => {
  before(() => TestBench.setupUnitTest())

  it('Emits a AcceptedTransportOrderCreatedEvent', async () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)
    const command = new CreateAcceptedTransportOrderCommandBuilder().build()

    const locationRepo = createStubInstance(Repository<Location>)
    locationRepo.findBy.resolves([
      new LocationBuilder().withUuid(command.pickupLocationUuid).build(),
      new LocationBuilder().withUuid(command.dropOffLocationUuid).build()
    ])

    const careUserRepo = createStubInstance(Repository<CareUser>)
    careUserRepo.findOneByOrFail.resolves(
      new CareUserEntityBuilder().withUuid(command.careUserUuid).build()
    )

    const contractTypeRepo = createStubInstance(Repository<ContractType>)
    contractTypeRepo.findOneByOrFail.resolves(
      new ContractTypeEntityBuilder().withUuid(command.contractUuid).build()
    )

    const maxTimeInVehicleFormulaRepo = createStubInstance(Repository<MaxTimeInVehicleFormula>)
    maxTimeInVehicleFormulaRepo.findOneByOrFail.resolves(
      new MaxTimeInVehicleFormulaEntityBuilder()
        .withUuid(command.maxTimeInVehicleFormulaUuid)
        .build()
    )

    const driverRepo = createStubInstance(Repository<Driver>)
    driverRepo.findBy.resolves([])

    const orderRepo = createStubInstance(Repository<AcceptedTransportOrder>)
    orderRepo.create.resolves(
      new AcceptedTransportOrderEntityBuilder().build()
    )

    const useCase = new CreateAcceptedTransportOrderUseCase(
      stubDataSource(),
      eventEmitter,
      orderRepo,
      createStubInstance(Repository<AcceptedTransportOrderPreferredDriver>),
      locationRepo,
      careUserRepo,
      contractTypeRepo,
      maxTimeInVehicleFormulaRepo,
      driverRepo,
      createStubInstance(ClientRepository)
    )

    const ato = await useCase.execute(command)

    expect(eventEmitter).toHaveEmitted(new AcceptedTransportOrderCreatedEvent(ato.uuid))
  })
})
