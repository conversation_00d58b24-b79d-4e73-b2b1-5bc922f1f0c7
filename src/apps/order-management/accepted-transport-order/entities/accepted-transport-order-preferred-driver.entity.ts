import { Column, CreateDateColumn, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Relation, UpdateDateColumn } from 'typeorm'
import { Driver } from '../../../resource-management/driver/entities/driver/driver.entity.js'
import { AcceptedTransportOrder } from './accepted-transport-order.entity.js'

@Entity()
export class AcceptedTransportOrderPreferredDriver {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Index()
  @Column({ type: 'uuid' })
  transportOrderUuid: string

  @ManyToOne(() => AcceptedTransportOrder)
  @JoinColumn({ name: 'transport_order_uuid' })
  transportOrder?: Relation<AcceptedTransportOrder>

  @Index()
  @Column({ type: 'uuid' })
  driverUuid: string

  @ManyToOne(() => Driver)
  @JoinColumn({ name: 'driver_uuid' })
  driver?: Relation<Driver>
}
