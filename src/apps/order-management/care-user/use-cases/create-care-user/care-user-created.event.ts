import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEvent } from '../../../../../modules/domain-events/domain-event.js'
import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'

@OneOfMeta(DomainEventLog, DomainEventType.CARE_USER_CREATED)
export class CareUserCreatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly careUserUuid: string

  constructor (careUserUuid: string) {
    this.careUserUuid = careUserUuid
  }
}

@RegisterDomainEvent(DomainEventType.CARE_USER_CREATED, 1)
export class CareUserCreatedEvent extends DomainEvent<CareUserCreatedEventContent> {
  constructor (careUserUuid: string) {
    super({ content: new CareUserCreatedEventContent(careUserUuid) })
  }
}
