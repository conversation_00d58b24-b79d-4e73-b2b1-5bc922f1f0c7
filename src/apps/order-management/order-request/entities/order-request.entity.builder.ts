import { randomUUID } from 'crypto'
import { WiseDate } from '@wisemen/wise-date'
import { ClientType } from '../../client/client-type.js'
import { OrderRequestStatus } from '../enums/order-request-status.js'
import { OrderRequest } from './order-request.entity.js'

export class OrderRequestEntityBuilder {
  private readonly orderRequest: OrderRequest

  constructor () {
    this.orderRequest = new OrderRequest()
    this.orderRequest.uuid = randomUUID()
    this.orderRequest.clientUuid = randomUUID()
    this.orderRequest.clientType = ClientType.ORGANIZATION
    this.orderRequest.status = OrderRequestStatus.DRAFT
    this.orderRequest.isRecurring = false
    this.orderRequest.startDate = null
    this.orderRequest.iterationIntervalWeeks = null
    this.orderRequest.remarksForDriver = null
    this.orderRequest.remarksForPlanner = null
  }

  withCreatedAt (createdAt: WiseDate): this {
    this.orderRequest.createdAt = createdAt.toDate()
    return this
  }

  withClientUuid (clientUuid: string): this {
    this.orderRequest.clientUuid = clientUuid
    return this
  }

  withClientType (clientType: ClientType): this {
    this.orderRequest.clientType = clientType
    return this
  }

  withStatus (status: OrderRequestStatus): this {
    this.orderRequest.status = status
    return this
  }

  withIsRecurring (isRecurring: boolean): this {
    this.orderRequest.isRecurring = isRecurring
    return this
  }

  withStartDate (startDate: WiseDate | null): this {
    this.orderRequest.startDate = startDate
    return this
  }

  withIterationIntervalWeeks (iterationIntervalWeeks: number | null): this {
    this.orderRequest.iterationIntervalWeeks = iterationIntervalWeeks
    return this
  }

  withRemarksForDriver (remarksForDriver: string | null): this {
    this.orderRequest.remarksForDriver = remarksForDriver
    return this
  }

  withRemarksForPlanner (remarksForPlanner: string | null): this {
    this.orderRequest.remarksForPlanner = remarksForPlanner
    return this
  }

  withContractUuid (contractUuid: string): this {
    this.orderRequest.contractUuid = contractUuid
    return this
  }

  withCareUserUuid (careUserUuid: string): this {
    this.orderRequest.careUserUuid = careUserUuid
    return this
  }

  withCreatedByUserUuid (createdByUserUuid: string): this {
    this.orderRequest.createdByUserUuid = createdByUserUuid
    return this
  }

  build (): OrderRequest {
    return this.orderRequest
  }
}
