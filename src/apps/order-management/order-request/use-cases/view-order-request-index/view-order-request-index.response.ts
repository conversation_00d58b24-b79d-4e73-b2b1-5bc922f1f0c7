import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { OrderRequest } from '../../entities/order-request.entity.js'
import { OrderRequestStatus } from '../../enums/order-request-status.js'
import { ClientType } from '../../../client/client-type.js'

class ContractTypeResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({ type: String })
  abbreviation: string

  constructor (uuid: string, name: string, abbreviation: string) {
    this.uuid = uuid
    this.name = name
    this.abbreviation = abbreviation
  }
}

class ContractResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: ContractTypeResponse })
  contractType: ContractTypeResponse

  constructor (uuid: string, contractType: ContractTypeResponse) {
    this.uuid = uuid
    this.contractType = contractType
  }
}

class CareUserResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  constructor (uuid: string, name: string) {
    this.uuid = uuid
    this.name = name
  }
}

class ClientResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({ enum: ClientType })
  type: ClientType

  constructor (uuid: string, name: string, type: ClientType) {
    this.uuid = uuid
    this.name = name
    this.type = type
  }
}

class UserResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, nullable: true })
  firstName: string | null

  @ApiProperty({ type: String, nullable: true })
  lastName: string | null

  constructor (uuid: string, firstName: string | null, lastName: string | null) {
    this.uuid = uuid
    this.firstName = firstName
    this.lastName = lastName
  }
}

export class OrderRequestResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String, format: 'uuid' })
  clientUuid: string

  @ApiProperty({ enum: ClientType })
  clientType: ClientType

  @ApiProperty({ enum: OrderRequestStatus })
  status: OrderRequestStatus

  @ApiProperty({ type: Boolean })
  isRecurring: boolean

  @ApiProperty({ type: String, format: 'date', nullable: true })
  startDate: string | null

  @ApiProperty({ type: Number, nullable: true })
  iterationIntervalWeeks: number | null

  @ApiProperty({ type: String, nullable: true })
  remarksForDriver: string | null

  @ApiProperty({ type: String, nullable: true })
  remarksForPlanner: string | null

  @ApiProperty({ type: ClientResponse })
  client: ClientResponse

  @ApiProperty({ type: ContractResponse })
  contract: ContractResponse

  @ApiProperty({ type: CareUserResponse })
  careUser: CareUserResponse

  @ApiProperty({ type: UserResponse })
  createdByUser: UserResponse

  constructor (orderRequest: OrderRequest) {
    assert(orderRequest.client !== undefined, 'client is required')
    assert(orderRequest.contract !== undefined, 'contract is required')
    assert(orderRequest.contract.contractType !== undefined, 'contract.contractType is required')
    assert(orderRequest.careUser !== undefined, 'careUser is required')
    assert(orderRequest.createdByUser !== undefined, 'createdByUser is required')

    this.uuid = orderRequest.uuid
    this.createdAt = orderRequest.createdAt.toISOString()
    this.updatedAt = orderRequest.updatedAt.toISOString()
    this.clientUuid = orderRequest.clientUuid
    this.clientType = orderRequest.clientType
    this.status = orderRequest.status
    this.isRecurring = orderRequest.isRecurring
    this.startDate = orderRequest.startDate?.toString() ?? null
    this.iterationIntervalWeeks = orderRequest.iterationIntervalWeeks
    this.remarksForDriver = orderRequest.remarksForDriver
    this.remarksForPlanner = orderRequest.remarksForPlanner

    this.client = new ClientResponse(
      orderRequest.client.uuid,
      orderRequest.client.name,
      orderRequest.client.type
    )

    this.contract = new ContractResponse(
      orderRequest.contract.uuid,
      new ContractTypeResponse(
        orderRequest.contract.contractType.uuid,
        orderRequest.contract.contractType.name,
        orderRequest.contract.contractType.abbreviation
      )
    )

    this.careUser = new CareUserResponse(
      orderRequest.careUser.uuid,
      orderRequest.careUser.name
    )

    this.createdByUser = new UserResponse(
      orderRequest.createdByUser.uuid,
      orderRequest.createdByUser.firstName,
      orderRequest.createdByUser.lastName
    )
  }
}

export class ViewOrderRequestIndexResponse extends PaginatedOffsetResponse<OrderRequestResponse> {
  @ApiProperty({ type: OrderRequestResponse, isArray: true })
  declare items: OrderRequestResponse[]

  constructor (items: OrderRequest[], total: number, limit: number, offset: number) {
    const result = items.map(orderRequest => new OrderRequestResponse(orderRequest))

    super(result, total, limit, offset)
  }
}
