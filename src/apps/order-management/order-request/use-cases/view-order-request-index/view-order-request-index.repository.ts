import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository, SelectQueryBuilder } from 'typeorm'
import { SortDirection, typeormPagination } from '@wisemen/pagination'
import { OrderRequest } from '../../entities/order-request.entity.js'
import { ClientRepository } from '../../../client/client-repository.js'
import { ClientNotFoundError } from '../../../client/errors/client-not-found.error.js'
import { ClientId } from '../../../client/client-id.js'
import { ViewOrderRequestIndexQuery } from './view-order-request-index.query.js'
import { ViewOrderRequestIndexSortQueryKey } from './view-order-request-index.sort-query.js'

@Injectable()
export class ViewOrderRequestIndexRepository {
  constructor (
    @InjectRepository(OrderRequest)
    private readonly orderRequestRepository: Repository<OrderRequest>,
    private readonly clientRepo: ClientRepository
  ) {}

  async findOrderRequests (query: ViewOrderRequestIndexQuery): Promise<[OrderRequest[], number]> {
    const pagination = typeormPagination(query.pagination)

    const queryBuilder = this.orderRequestRepository.createQueryBuilder('orderRequest')
      .leftJoinAndSelect('orderRequest.contract', 'contract')
      .leftJoinAndSelect('contract.contractType', 'contractType')
      .leftJoinAndSelect('orderRequest.careUser', 'careUser')
      .leftJoinAndSelect('orderRequest.createdByUser', 'createdByUser')

    this.applySorting(queryBuilder, query)

    const [items, totalCount] = await queryBuilder
      .skip(pagination.skip)
      .take(pagination.take)
      .getManyAndCount()

    const clientIds = items.map(order => new ClientId(order.clientUuid, order.clientType))
    const clients = await this.clientRepo.findByIds(clientIds)

    for (const order of items) {
      const client = clients.find(client =>
        client.hasId(new ClientId(order.clientUuid, order.clientType))
      )
      assert(client !== undefined, new ClientNotFoundError())
      order.client = client
    }

    return [items, totalCount]
  }

  private applySorting (
    queryBuilder: SelectQueryBuilder<OrderRequest>,
    query: ViewOrderRequestIndexQuery
  ): void {
    if (query.sort && query.sort.length > 0) {
      query.sort.forEach((sortItem, index) => {
        const orderMethod = index === 0 ? 'orderBy' : 'addOrderBy'

        switch (sortItem.key) {
          case ViewOrderRequestIndexSortQueryKey.STATUS:
            queryBuilder[orderMethod]('orderRequest.status', sortItem.order === SortDirection.ASC ? 'ASC' : 'DESC')
            break
          case ViewOrderRequestIndexSortQueryKey.START_DATE:
            queryBuilder[orderMethod]('orderRequest.startDate', sortItem.order === SortDirection.ASC ? 'ASC' : 'DESC')
            break
        }
      })
    } else {
      queryBuilder
        .orderBy('orderRequest.createdAt', 'DESC')
    }
  }
}
