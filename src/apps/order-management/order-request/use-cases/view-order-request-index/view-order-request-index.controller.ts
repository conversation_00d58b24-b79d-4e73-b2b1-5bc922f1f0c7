import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../../../modules/permission/permission.enum.js'
import { Permissions } from '../../../../../modules/permission/permission.decorator.js'
import { ViewOrderRequestIndexQuery } from './view-order-request-index.query.js'
import { ViewOrderRequestIndexResponse } from './view-order-request-index.response.js'
import { ViewOrderRequestIndexUseCase } from './view-order-request-index.use-case.js'

@ApiTags('Order Request')
@ApiOAuth2([])
@Controller('order-requests')
export class ViewOrderRequestIndexController {
  constructor (
    private readonly viewOrderRequestIndexUseCase: ViewOrderRequestIndexUseCase
  ) {}

  @Get()
  @Permissions(Permission.ORDER_REQUEST_READ)
  @ApiOkResponse({ type: ViewOrderRequestIndexResponse })
  async viewOrderRequestIndex (
    @Query() query: ViewOrderRequestIndexQuery
  ): Promise<ViewOrderRequestIndexResponse> {
    return this.viewOrderRequestIndexUseCase.execute(query)
  }
}
