import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { OrderRequest } from '../../entities/order-request.entity.js'
import { ClientModule } from '../../../client/client-module.js'
import { ViewOrderRequestIndexController } from './view-order-request-index.controller.js'
import { ViewOrderRequestIndexUseCase } from './view-order-request-index.use-case.js'
import { ViewOrderRequestIndexRepository } from './view-order-request-index.repository.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([OrderRequest]),
    ClientModule
  ],
  controllers: [ViewOrderRequestIndexController],
  providers: [ViewOrderRequestIndexUseCase, ViewOrderRequestIndexRepository],
  exports: [ViewOrderRequestIndexUseCase]
})
export class ViewOrderRequestIndexModule {}
