import { ApiProperty } from '@nestjs/swagger'
import { IsEnum } from 'class-validator'
import {
  SortDirection,
  SortDirectionApiProperty,
  SortQuery
} from '../../../../../utils/query/search.query.js'

export enum ViewOrderRequestIndexSortQueryKey {
  STATUS = 'status',
  START_DATE = 'startDate'
}

export class ViewOrderRequestIndexSortQuery extends SortQuery {
  @ApiProperty({ enum: ViewOrderRequestIndexSortQueryKey, enumName: 'ViewOrderRequestIndexSortQueryKey' })
  @IsEnum(ViewOrderRequestIndexSortQueryKey)
  key: ViewOrderRequestIndexSortQueryKey

  @SortDirectionApiProperty()
  @IsEnum(SortDirection)
  order: SortDirection
}
