import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { ViewOrderRequestIndexRepository } from './view-order-request-index.repository.js'
import { ViewOrderRequestIndexQuery } from './view-order-request-index.query.js'
import { ViewOrderRequestIndexResponse } from './view-order-request-index.response.js'

@Injectable()
export class ViewOrderRequestIndexUseCase {
  constructor (
    private readonly repository: ViewOrderRequestIndexRepository
  ) {}

  async execute (query: ViewOrderRequestIndexQuery): Promise<ViewOrderRequestIndexResponse> {
    const pagination = typeormPagination(query.pagination)
    const [orderRequests, totalCount] = await this.repository.findOrderRequests(query)

    return new ViewOrderRequestIndexResponse(
      orderRequests,
      totalCount,
      pagination.take,
      pagination.skip
    )
  }
}
