import {
  ArrayMinSize,
  Equals,
  <PERSON><PERSON>rray,
  IsObject,
  IsOptional,
  ValidateNested
} from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { ViewOrderRequestIndexSortQuery } from './view-order-request-index.sort-query.js'

export class ViewOrderRequestIndexQuery extends PaginatedOffsetSearchQuery {
  @ApiProperty({ type: ViewOrderRequestIndexSortQuery, isArray: true, required: false })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsObject({ each: true })
  @Type(() => ViewOrderRequestIndexSortQuery)
  @ValidateNested({ each: true })
  sort?: ViewOrderRequestIndexSortQuery[]

  @Equals(undefined)
  filter?: never

  @Equals(undefined)
  search?: never
}
