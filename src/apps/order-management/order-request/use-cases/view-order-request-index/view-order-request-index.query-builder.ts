import { PaginatedOffsetQuery } from '@wisemen/pagination'
import { SortDirection } from '../../../../../utils/query/search.query.js'
import { ViewOrderRequestIndexQuery } from './view-order-request-index.query.js'
import { ViewOrderRequestIndexSortQuery, ViewOrderRequestIndexSortQueryKey } from './view-order-request-index.sort-query.js'

export class ViewOrderRequestIndexQueryBuilder {
  private readonly query: ViewOrderRequestIndexQuery

  constructor () {
    this.query = new ViewOrderRequestIndexQuery()
    this.query.pagination = { limit: 10, offset: 0 }
  }

  withPagination (pagination: PaginatedOffsetQuery): this {
    this.query.pagination = pagination
    return this
  }

  withSort (key: ViewOrderRequestIndexSortQueryKey, order: SortDirection): this {
    this.query.sort ??= []

    const sortQuery = new ViewOrderRequestIndexSortQuery()
    sortQuery.key = key
    sortQuery.order = order

    this.query.sort.push(sortQuery)
    return this
  }

  build (): ViewOrderRequestIndexQuery {
    return this.query
  }
}
