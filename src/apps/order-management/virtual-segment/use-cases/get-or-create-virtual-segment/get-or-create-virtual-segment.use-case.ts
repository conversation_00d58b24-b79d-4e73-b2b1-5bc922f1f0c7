import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { transaction } from '@wisemen/nestjs-typeorm'
import { DataSource } from 'typeorm'

import { VirtualSegmentBuilder } from '../../virtual-segment.builder.js'
import { VirtualSegment } from '../../virtual-segment.entity.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { Duration } from '../../../../../utils/quantities/duration.js'
import { Distance } from '../../../../../utils/quantities/distance.js'
import { Location } from '../../../../../modules/location/entities/location.entity.js'
import { GetOrCreateVirtualSegmentQuery } from './get-or-create-virtual-segment.query.js'
import { GetOrCreateVirtualSegmentRepository } from './get-or-create-virtual-segment.repository.js'
import { GetOrCreateVirtualSegmentResponse } from './get-or-create-virtual-segment.response.js'
import { VirtualSegmentCreatedEvent } from './virtual-segment-created.event.js'
import {
  VirtualSegmentRoute,
  VirtualSegmentRouteCalculator
} from './virtual-segment-route.calculator.js'

@Injectable()
export class GetOrCreateVirtualSegmentUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly repository: GetOrCreateVirtualSegmentRepository,
    private readonly eventEmitter: DomainEventEmitter,
    private readonly routeCalculator: VirtualSegmentRouteCalculator
  ) {}

  async getOrCreateVirtualSegment (
    query: GetOrCreateVirtualSegmentQuery
  ): Promise<GetOrCreateVirtualSegmentResponse> {
    let segment = await this.repository.getExistingSegment(query.fromUuid, query.toUuid)

    if (segment === null) {
      segment = await this.createSegment(query)
    }

    return new GetOrCreateVirtualSegmentResponse(segment)
  }

  private async createSegment (query: GetOrCreateVirtualSegmentQuery): Promise<VirtualSegment> {
    const { from, to } = await this.repository.findLocations(query.fromUuid, query.toUuid)
    const route = await this.calculateRoute(from, to)

    const newSegment = new VirtualSegmentBuilder()
      .withFromUuid(from.uuid)
      .withToUuid(to.uuid)
      .withDistance(route.distance)
      .withDuration(route.duration)
      .build()

    const event = new VirtualSegmentCreatedEvent(newSegment.uuid)

    await transaction(this.dataSource, async () => {
      await this.repository.insertSegment(newSegment)
      await this.eventEmitter.emitOne(event)
    })

    return newSegment
  }

  private async calculateRoute (from: Location, to: Location): Promise<VirtualSegmentRoute> {
    if (from.uuid === to.uuid) {
      return { duration: new Duration(0, 's'), distance: new Distance(0, 'm') }
    } else {
      const fromCoordinates = from.address.coordinates
      const toCoordinates = to.address.coordinates
      assert(fromCoordinates != null && toCoordinates != null, 'Coordinates cannot be null')
      return await this.routeCalculator.calculateRoute(fromCoordinates, toCoordinates)
    }
  }
}
