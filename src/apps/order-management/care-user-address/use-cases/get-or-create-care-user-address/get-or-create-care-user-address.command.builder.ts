import { randomUUID } from 'crypto'
import { AddressCommandBuilder } from '../../../../../utils/address/address-command.builder.js'
import { AddressCommand } from '../../../../../utils/address/address-command.js'
import { GetOrCreateCareUserAddressCommand } from './get-or-create-care-user-address.command.js'

export class GetOrCreateCareUserAddressCommandBuilder {
  private readonly command: GetOrCreateCareUserAddressCommand

  constructor () {
    this.command = new GetOrCreateCareUserAddressCommand()
    this.command.careUserUuid = randomUUID()
    this.command.address = new AddressCommandBuilder().build()
  }

  withCareUserUuid (careUserUuid: string): this {
    this.command.careUserUuid = careUserUuid
    return this
  }

  withAddress (address: AddressCommand): this {
    this.command.address = address
    return this
  }

  build (): GetOrCreateCareUserAddressCommand {
    return this.command
  }
}
