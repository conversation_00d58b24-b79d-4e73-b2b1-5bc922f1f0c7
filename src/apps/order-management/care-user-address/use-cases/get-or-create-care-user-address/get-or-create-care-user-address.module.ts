import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { CareUserAddress } from '../../care-user-address.entity.js'
import { GetOrCreateCareUserAddressController } from './get-or-create-care-user-address.controller.js'
import { GetOrCreateCareUserAddressUseCase } from './get-or-create-care-user-address.use-case.js'
import { GetOrCreateCareUserAddressRepository } from './get-or-create-care-user-address.repository.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([CareUserAddress])
  ],
  controllers: [GetOrCreateCareUserAddressController],
  providers: [GetOrCreateCareUserAddressUseCase, GetOrCreateCareUserAddressRepository],
  exports: [GetOrCreateCareUserAddressUseCase]
})
export class GetOrCreateCareUserAddressModule {}
