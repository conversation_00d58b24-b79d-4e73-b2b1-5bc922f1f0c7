import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { CareUserAddress } from '../../../care-user-address.entity.js'
import { CareUserAddressEntityBuilder } from '../../../care-user-address.entity.builder.js'
import { CareUserEntityBuilder } from '../../../../care-user/builders/care-user.entity.builder.js'
import { CareUser } from '../../../../care-user/entities/care-user.entity.js'
import { GetOrCreateCareUserAddressCommandBuilder } from '../get-or-create-care-user-address.command.builder.js'
import { AddressCommandBuilder } from '../../../../../../utils/address/address-command.builder.js'
import { AddressBuilder } from '../../../../../../utils/address/address.builder.js'

describe('Get or create care user address e2e test', () => {
  let setup: EndToEndTestSetup
  let adminUser: TestUser
  let careUser: CareUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    adminUser = await setup.authContext.getAdminUser()

    careUser = new CareUserEntityBuilder().build()
    await setup.dataSource.manager.insert(CareUser, careUser)
  })

  after(async () => {
    await setup.teardown()
  })

  it('creates a new care user address when none exists', async () => {
    const command = new GetOrCreateCareUserAddressCommandBuilder()
      .withCareUserUuid(careUser.uuid)
      .withAddress(new AddressCommandBuilder()
        .withPlaceId('new-place-id')
        .build())
      .build()

    const response = await request(setup.httpServer)
      .post('/care-user-addresses/get-or-create')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command)

    expect(response).toHaveStatus(201)
    expect(response.body.careUserUuid).toBe(careUser.uuid)
    expect(response.body.address.placeId).toBe('new-place-id')
  })

  it('returns existing care user address when one already exists', async () => {
    const existingAddress = new CareUserAddressEntityBuilder()
      .withCareUserUuid(careUser.uuid)
      .withAddress(new AddressBuilder()
        .withPlaceId('existing-place-id')
        .build())
      .withStopDuration(10)
      .withActionDuration(5)
      .withRemarksForDriver('Existing driver remarks')
      .withRemarksForPlanner('Existing planner remarks')
      .build()

    await setup.dataSource.manager.insert(CareUserAddress, existingAddress)

    const command = new GetOrCreateCareUserAddressCommandBuilder()
      .withCareUserUuid(careUser.uuid)
      .withAddress(new AddressCommandBuilder()
        .withPlaceId('existing-place-id')
        .build())
      .build()

    const response = await request(setup.httpServer)
      .post('/care-user-addresses/get-or-create')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command)

    expect(response).toHaveStatus(201)
    expect(response.body.uuid).toBe(existingAddress.uuid)
    expect(response.body.careUserUuid).toBe(careUser.uuid)
    expect(response.body.address.placeId).toBe('existing-place-id')
  })

  it('creates different addresses for same care user with different place IDs', async () => {
    const command1 = new GetOrCreateCareUserAddressCommandBuilder()
      .withCareUserUuid(careUser.uuid)
      .withAddress(new AddressCommandBuilder()
        .withPlaceId('place-1')
        .build())
      .build()

    const command2 = new GetOrCreateCareUserAddressCommandBuilder()
      .withCareUserUuid(careUser.uuid)
      .withAddress(new AddressCommandBuilder()
        .withPlaceId('place-2')
        .build())
      .build()

    const response1 = await request(setup.httpServer)
      .post('/care-user-addresses/get-or-create')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command1)

    const response2 = await request(setup.httpServer)
      .post('/care-user-addresses/get-or-create')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command2)

    expect(response1).toHaveStatus(201)
    expect(response2).toHaveStatus(201)
    expect(response1.body.uuid).not.toBe(response2.body.uuid)
    expect(response1.body.address.placeId).toBe('place-1')
    expect(response2.body.address.placeId).toBe('place-2')
  })
})
