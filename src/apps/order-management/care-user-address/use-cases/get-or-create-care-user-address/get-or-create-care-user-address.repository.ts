import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { CareUserAddress } from '../../care-user-address.entity.js'

@Injectable()
export class GetOrCreateCareUserAddressRepository {
  constructor (
    @InjectRepository(CareUserAddress)
    private readonly careUserAddressRepository: Repository<CareUserAddress>
  ) {}

  async findByPlaceIdAndCareUserUuid (
    placeId: string,
    careUserUuid: string
  ): Promise<CareUserAddress | null> {
    return this.careUserAddressRepository
      .createQueryBuilder('care_user_address')
      .where('care_user_address.care_user_uuid = :careUserUuid', { careUserUuid })
      .andWhere('care_user_address.address->>\'placeId\' = :placeId', { placeId })
      .getOne()
  }

  async insert (careUserAddress: CareUserAddress): Promise<void> {
    await this.careUserAddressRepository.insert(careUserAddress)
  }
}
