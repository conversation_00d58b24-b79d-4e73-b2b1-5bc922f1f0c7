import { Body, Controller, Post } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiCreatedResponse } from '@nestjs/swagger'
import { Permission } from '../../../../../modules/permission/permission.enum.js'
import { Permissions } from '../../../../../modules/permission/permission.decorator.js'
import { GetOrCreateCareUserAddressCommand } from './get-or-create-care-user-address.command.js'
import { GetOrCreateCareUserAddressResponse } from './get-or-create-care-user-address.response.js'
import { GetOrCreateCareUserAddressUseCase } from './get-or-create-care-user-address.use-case.js'

@ApiTags('Care User Address')
@ApiOAuth2([])
@Controller('care-user-addresses/get-or-create')
export class GetOrCreateCareUserAddressController {
  constructor (
    private readonly getOrCreateCareUserAddressUseCase: GetOrCreateCareUserAddressUseCase
  ) {}

  @Post()
  @Permissions(Permission.ORDER_REQUEST_CREATE)
  @ApiCreatedResponse({ type: GetOrCreateCareUserAddressResponse })
  async getOrCreateCareUserAddress (
    @Body() command: GetOrCreateCareUserAddressCommand
  ): Promise<GetOrCreateCareUserAddressResponse> {
    return this.getOrCreateCareUserAddressUseCase.execute(command)
  }
}
