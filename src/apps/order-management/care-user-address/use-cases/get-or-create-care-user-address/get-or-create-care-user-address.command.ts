import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsUUID } from 'class-validator'
import { AddressCommand } from '../../../../../utils/address/address-command.js'
import { IsAddress } from '../../../../../utils/address/is-address.validator.js'

export class GetOrCreateCareUserAddressCommand {
  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  @IsNotEmpty()
  careUserUuid: string

  @ApiProperty({ type: AddressCommand })
  @IsAddress({ placeIdRequired: true })
  address: AddressCommand
}
