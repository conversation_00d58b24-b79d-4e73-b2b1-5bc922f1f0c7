import { ApiProperty } from '@nestjs/swagger'
import { CareUserAddress } from '../../care-user-address.entity.js'
import { AddressResponse } from '../../../../../utils/address/address-response.js'

export class GetOrCreateCareUserAddressResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'uuid' })
  careUserUuid: string

  @ApiProperty({ type: AddressResponse })
  address: AddressResponse

  @ApiProperty({ type: Number })
  stopDuration: number

  @ApiProperty({ type: Number })
  actionDuration: number

  @ApiProperty({ type: String, nullable: true })
  remarksForDriver: string | null

  @ApiProperty({ type: String, nullable: true })
  remarksForPlanner: string | null

  @ApiProperty({ type: Boolean, description: 'Indicates if this address was newly created' })
  wasCreated: boolean

  constructor (careUserAddress: CareUserAddress, wasCreated: boolean) {
    this.uuid = careUserAddress.uuid
    this.careUserUuid = careUserAddress.careUserUuid
    this.address = new AddressResponse(careUserAddress.address)
    this.stopDuration = careUserAddress.stopDuration
    this.actionDuration = careUserAddress.actionDuration
    this.remarksForDriver = careUserAddress.remarksForDriver
    this.remarksForPlanner = careUserAddress.remarksForPlanner
    this.wasCreated = wasCreated
  }
}
