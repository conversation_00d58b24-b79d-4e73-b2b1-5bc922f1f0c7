import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { DataSource } from 'typeorm'
import { transaction } from '@wisemen/nestjs-typeorm'
import { CareUserAddressEntityBuilder } from '../../care-user-address.entity.builder.js'
import { GetOrCreateCareUserAddressCommand } from './get-or-create-care-user-address.command.js'
import { GetOrCreateCareUserAddressRepository } from './get-or-create-care-user-address.repository.js'
import { GetOrCreateCareUserAddressResponse } from './get-or-create-care-user-address.response.js'

@Injectable()
export class GetOrCreateCareUserAddressUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly repository: GetOrCreateCareUserAddressRepository
  ) {}

  async execute (
    command: GetOrCreateCareUserAddressCommand
  ): Promise<GetOrCreateCareUserAddressResponse> {
    assert(command.address.placeId !== null, 'Place ID is required')
    let careUserAddress = await this.repository.findByPlaceIdAndCareUserUuid(
      command.address.placeId,
      command.careUserUuid
    )

    if (careUserAddress != null) {
      return new GetOrCreateCareUserAddressResponse(careUserAddress, false)
    }

    careUserAddress = new CareUserAddressEntityBuilder()
      .withAddress(command.address.parse())
      .withCareUserUuid(command.careUserUuid)
      .withStopDuration(0)
      .withActionDuration(0)
      .withRemarksForDriver(null)
      .withRemarksForPlanner(null)
      .build()

    await transaction(this.dataSource, async () => {
      await this.repository.insert(careUserAddress)
    })

    return new GetOrCreateCareUserAddressResponse(careUserAddress, true)
  }
}
