import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn, Relation } from 'typeorm'
import { CareUser } from '../care-user/entities/care-user.entity.js'
import { AddressColumn } from '../../../utils/address/address-column.js'
import { Address } from '../../../utils/address/address.js'

@Entity()
export class CareUserAddress {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @AddressColumn()
  address: Address

  @Column({ type: 'uuid' })
  careUserUuid: string

  @ManyToOne(() => CareUser)
  @JoinColumn({ name: 'care_user_uuid' })
  careUser?: Relation<CareUser>

  @Column({ type: 'int', default: 0 })
  stopDuration: number

  @Column({ type: 'int', default: 0 })
  actionDuration: number

  @Column({ type: 'varchar', nullable: true })
  remarksForDriver: string | null

  @Column({ type: 'varchar', nullable: true })
  remarksForPlanner: string | null
}
