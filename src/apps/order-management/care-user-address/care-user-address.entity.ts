import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn, Relation, Unique } from 'typeorm'
import { CareUser } from '../care-user/entities/care-user.entity.js'

@Entity()
@Unique(['placeId', 'careUserUuid'])
export class CareUserAddress {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @Column({ type: 'string' })
  placeId: string

  @Column({ type: 'uuid' })
  careUserUuid: string

  @ManyToOne(() => CareUser)
  @JoinColumn({ name: 'care_user_uuid' })
  careUser?: Relation<CareUser>

  @Column({ type: 'number', default: 0 })
  stopDuration: number

  @Column({ type: 'number', default: 0 })
  actionDuration: number

  @Column({ type: 'string', nullable: true })
  remarksForDriver: string | null

  @Column({ type: 'string', nullable: true })
  remarksForPlanner: string | null
}
