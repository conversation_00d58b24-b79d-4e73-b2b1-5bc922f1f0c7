import { randomUUID } from 'crypto'
import { AddressBuilder } from '../../../utils/address/address.builder.js'
import { Address } from '../../../utils/address/address.js'
import { CareUserAddress } from './care-user-address.entity.js'

export class CareUserAddressEntityBuilder {
  private readonly careUserAddress: CareUserAddress

  constructor () {
    this.careUserAddress = new CareUserAddress()
    this.careUserAddress.uuid = randomUUID()
    this.careUserAddress.address = new AddressBuilder().build()
    this.careUserAddress.careUserUuid = randomUUID()
    this.careUserAddress.stopDuration = 0
    this.careUserAddress.actionDuration = 0
    this.careUserAddress.remarksForDriver = null
    this.careUserAddress.remarksForPlanner = null
  }

  withUuid (uuid: string): this {
    this.careUserAddress.uuid = uuid
    return this
  }

  withAddress (address: Address): this {
    this.careUserAddress.address = address
    return this
  }

  withCareUserUuid (careUserUuid: string): this {
    this.careUserAddress.careUserUuid = careUserUuid
    return this
  }

  withStopDuration (stopDuration: number): this {
    this.careUserAddress.stopDuration = stopDuration
    return this
  }

  withActionDuration (actionDuration: number): this {
    this.careUserAddress.actionDuration = actionDuration
    return this
  }

  withRemarksForDriver (remarksForDriver: string | null): this {
    this.careUserAddress.remarksForDriver = remarksForDriver
    return this
  }

  withRemarksForPlanner (remarksForPlanner: string | null): this {
    this.careUserAddress.remarksForPlanner = remarksForPlanner
    return this
  }

  build (): CareUserAddress {
    return this.careUserAddress
  }
}
