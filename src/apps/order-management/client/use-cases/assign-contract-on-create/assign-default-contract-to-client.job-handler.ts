import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@wisemen/pgboss-nestjs-job'
import { Injectable } from '@nestjs/common'
import { AssignDefaultContractToClientJob, AssignDefaultContractToClientJobData } from './assign-default-contract-to-client.job.js'
import { AssignDefaultContractToClientUseCase } from './assign-default-contract-to-client.use-case.js'

@Injectable()
@PgBossJobHandler(AssignDefaultContractToClientJob)
export class AssignDefaultContractToClientJobHandler
  extends JobHandler<AssignDefaultContractToClientJob> {
  constructor (
    private readonly useCase: AssignDefaultContractToClientUseCase
  ) {
    super()
  }

  async run (jobData: AssignDefaultContractToClientJobData): Promise<void> {
    await this.useCase.execute(jobData.clientUuid, jobData.clientType)
  }
}
