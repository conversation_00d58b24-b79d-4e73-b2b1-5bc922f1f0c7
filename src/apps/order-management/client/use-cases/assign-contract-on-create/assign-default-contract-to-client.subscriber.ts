import { Injectable } from '@nestjs/common'
import { PgBossScheduler } from '@wisemen/pgboss-nestjs-job'
import { Subscribe } from '../../../../../modules/domain-events/subscribe.decorator.js'
import { CareUserCreatedEvent } from '../../../care-user/use-cases/create-care-user/care-user-created.event.js'
import { OrganizationCreatedEvent } from '../../../organization/use-cases/create-organization/organization-created.event.js'
import { ClientType } from '../../client-type.js'
import { AssignDefaultContractToClientJob } from './assign-default-contract-to-client.job.js'

@Injectable()
export class AssignDefaultContractToClientSubscriber {
  constructor (
    private readonly jobScheduler: PgBossScheduler
  ) {}

  @Subscribe(CareUserCreatedEvent)
  @Subscribe(OrganizationCreatedEvent)
  async handle (
    events: CareUserCreatedEvent[] | OrganizationCreatedEvent[]
  ): Promise<void> {
    const clients = events.map((event) => {
      if (event instanceof CareUserCreatedEvent) {
        return { clientUuid: event.content.careUserUuid, clientType: ClientType.CARE_USER }
      } else if (event instanceof OrganizationCreatedEvent) {
        return { clientUuid: event.content.organizationUuid, clientType: ClientType.ORGANIZATION }
      }
      throw new Error('Unknown event type')
    })
    const jobs = clients.map(client =>
      new AssignDefaultContractToClientJob(client.clientUuid, client.clientType)
    )
    await this.jobScheduler.scheduleJobs(jobs)
  }
}
