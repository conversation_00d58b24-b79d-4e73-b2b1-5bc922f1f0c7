import { BaseJob, BaseJobData, PgBossJob } from '@wisemen/pgboss-nestjs-job'
import { ClientType } from '../../client-type.js'
import { QueueName } from '../../../../../modules/pgboss/enums/queue-name.enum.js'

export interface AssignDefaultContractToClientJobData extends BaseJobData {
  clientUuid: string
  clientType: ClientType
}

@PgBossJob(QueueName.SYSTEM)
export class AssignDefaultContractToClientJob
  extends BaseJob<AssignDefaultContractToClientJobData> {
  constructor (clientUuid: string, clientType: ClientType) {
    super({ clientUuid, clientType })
  }
}
