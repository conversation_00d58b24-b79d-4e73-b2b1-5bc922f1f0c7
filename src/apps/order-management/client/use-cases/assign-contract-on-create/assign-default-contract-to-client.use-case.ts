import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, In, Repository } from 'typeorm'
import { ClientType } from '../../client-type.js'
import { Contract } from '../../../contract/entities/contract.entity.js'
import { ContractType } from '../../../contract-type/entities/contract-type.entity.js'
import { ContractTypeName } from '../../../contract-type/enums/contract-type-name.enum.js'
import { ClientId } from '../../client-id.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { ContractEntityBuilder } from '../../../contract/entities/contract.entity.builder.js'
import { DomainEvent } from '../../../../../modules/domain-events/domain-event.js'
import { DefaultContractAssignedEvent } from './default-contract-assigned-to-client.event.js'

@Injectable()
export class AssignDefaultContractToClientUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(Contract) private readonly contractRepo: Repository<Contract>,
    @InjectRepository(ContractType) private readonly contractTypeRepo: Repository<ContractType>
  ) {}

  async execute (clientUuid: string, clientType: ClientType): Promise<void> {
    const contractTypes = await this.contractTypeRepo.find({
      where: {
        name: In([
          ContractTypeName.PRIVATE,
          ContractTypeName.PRIVATE_WITH_WAIT,
          ContractTypeName.PAB,
          ContractTypeName.PAB_WITH_ASSISTANCE
        ])
      }
    })

    const contracts: Contract[] = []
    const events: DomainEvent[] = []

    for (const contractType of contractTypes) {
      const client = new ClientId(clientUuid, clientType)
      contracts.push(new ContractEntityBuilder()
        .withContractTypeUuid(contractType.uuid)
        .withClientId(client)
        .build())

      events.push(new DefaultContractAssignedEvent(client, contractType.uuid))
    }

    await transaction(this.dataSource, async () => {
      await this.contractRepo.insert(contracts)
      await this.eventEmitter.emit(events)
    })
  }
}
