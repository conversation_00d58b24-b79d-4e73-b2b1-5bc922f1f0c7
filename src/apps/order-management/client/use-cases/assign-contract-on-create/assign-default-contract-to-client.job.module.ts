import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { ContractType } from '../../../contract-type/entities/contract-type.entity.js'
import { Contract } from '../../../contract/entities/contract.entity.js'
import { AssignDefaultContractToClientJobHandler } from './assign-default-contract-to-client.job-handler.js'
import { AssignDefaultContractToClientUseCase } from './assign-default-contract-to-client.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Contract,
      ContractType
    ]),
    DomainEventEmitterModule
  ],
  providers: [
    AssignDefaultContractToClientJobHandler,
    AssignDefaultContractToClientUseCase
  ]
})
export class AssignDefaultContractToClientJobModule {}
