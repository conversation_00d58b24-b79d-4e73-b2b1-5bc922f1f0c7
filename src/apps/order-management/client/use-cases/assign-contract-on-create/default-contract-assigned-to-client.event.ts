import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEvent } from '../../../../../modules/domain-events/domain-event.js'
import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'
import { ClientType } from '../../client-type.js'
import { ClientId } from '../../client-id.js'

@OneOfMeta(DomainEventLog, DomainEventType.DEFAULT_CONTRACT_ASSIGNED)
export class DefaultContractAssignedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly clientUuid: string

  @ApiProperty({ enum: ClientType })
  readonly clientType: ClientType

  @ApiProperty({ format: 'uuid' })
  readonly contractTypeUuid: string

  constructor (clientId: ClientId, contractTypeUuid: string) {
    this.clientUuid = clientId.uuid
    this.clientType = clientId.type
    this.contractTypeUuid = contractTypeUuid
  }
}

@RegisterDomainEvent(DomainEventType.DEFAULT_CONTRACT_ASSIGNED, 1)
export class DefaultContractAssignedEvent extends DomainEvent {
  constructor (clientId: ClientId, contractTypeUuid: string) {
    super({
      content: new DefaultContractAssignedEventContent(clientId, contractTypeUuid)
    })
  }
}
