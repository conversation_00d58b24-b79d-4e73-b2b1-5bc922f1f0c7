import {
  Column,
  CreateDateColumn,
  Entity,
  Index, JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn, Relation,
  UpdateDateColumn
} from 'typeorm'
import { Branch } from '../../../resource-management/branch/branch.entity.js'
import { Driver } from '../../../resource-management/driver/entities/driver/driver.entity.js'
import { Shift } from '../../../resource-management/shift/entities/shift.entity.js'
import { Location } from '../../../../modules/location/entities/location.entity.js'
import {
  Duration,
  DurationColumn
} from '../../../../utils/quantities/duration.js'
import { DEFAULT_MAX_SHIFT_DURATION, DEFAULT_SHIFT_CAPACITY } from '../../../resource-management/shift/constants.js'
import { Simulation } from './simulation.entity.js'
import { SimulatedPlanningSequence } from './simulated-planning-sequence.entity.js'

@Entity()
export class SimulatedShift {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Index()
  @Column({ type: 'uuid' })
  simulationUuid: string

  @ManyToOne(() => Simulation)
  @JoinColumn({ name: 'simulation_uuid' })
  simulation?: Relation<Simulation>

  @OneToMany(() => SimulatedPlanningSequence, planningSequence => planningSequence.simulatedShift)
  planningSequences: Relation<SimulatedPlanningSequence[]>

  @Index()
  @Column({ type: 'uuid', nullable: true })
  originalShiftUuid: string | null

  @ManyToOne(() => Shift)
  @JoinColumn({ name: 'original_shift_uuid' })
  originalShift?: Relation<Shift> | null

  @Index()
  @Column({ type: 'timestamptz' })
  from: Date

  @Column({ type: 'timestamptz' })
  until: Date

  @Column({ type: 'varchar', nullable: true })
  licensePlate: string | null

  @Column({ type: 'uuid' })
  startLocationUuid: string

  @ManyToOne(() => Location)
  @JoinColumn({ name: 'start_location_uuid' })
  startLocation?: Relation<Location>

  @Column({ type: 'uuid' })
  stopLocationUuid: string

  @ManyToOne(() => Location)
  @JoinColumn({ name: 'stop_location_uuid' })
  stopLocation?: Relation<Location>

  @Column({ type: 'int', default: DEFAULT_SHIFT_CAPACITY })
  capacity: number

  @DurationColumn({ default: DEFAULT_MAX_SHIFT_DURATION.valueOf() })
  maxDuration: Duration

  @Index()
  @Column({ type: 'uuid', nullable: true })
  driverUuid: string | null

  @ManyToOne(() => Driver, driver => driver.uuid)
  @JoinColumn({ name: 'driver_uuid' })
  driver?: Relation<Driver> | null

  @Column({ type: 'uuid' })
  branchUuid: string

  @ManyToOne(() => Branch, branch => branch.uuid)
  @JoinColumn({ name: 'branch_uuid' })
  branch: Relation<Branch>

  @Column({ type: 'bool', nullable: true })
  isUsedInSolution: boolean | null

  @Column({ type: 'bool', nullable: true, default: false })
  isVirtual: boolean
}
