import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { SimulatedShift } from '../../entities/simulated-shift.entity.js'
import { SimulatedTransportOrder } from '../../entities/simulated-transport-order.entity.js'
import {
  RequestOptimisationCombinableOrdersCommand,
  RequestOptimisationCommand,
  RequestOptimisationConfigCommand,
  RequestOptimisationFormulaCommand,
  RequestOptimisationLocationCommand,
  RequestOptimisationOrderCommand,
  RequestOptimisationOrdersCommand,
  RequestOptimisationTimeWindowCommand,
  RequestOptimisationVehicleCommand
} from '../../../optimisation/create-planning-optimisation-request.command.js'
import {
  RequestOptimisationCommandBuilder
} from '../../../optimisation/request-optimisation-command-builder/request-optimisation-command.builder.js'
import {
  RequestOptimisationFormulaCommandBuilder
} from '../../../optimisation/request-optimisation-command-builder/request-optimisation-formula-command.builder.js'
import { Simulation } from '../../entities/simulation.entity.js'
import {
  RequestOptimisationConfigCommandBuilder
} from '../../../optimisation/request-optimisation-command-builder/request-optimisation-config-command.builder.js'
import {
  RequestOptimisationOrdersCommandBuilder
} from '../../../optimisation/request-optimisation-command-builder/request-optimisation-orders-command.builder.js'
import {
  CombinationRule
} from '../../../../order-management/contract-type/enums/combination-rule.enum.js'
import {
  RequestOptimisationOrderCommandBuilder
} from '../../../optimisation/request-optimisation-command-builder/request-optimisation-order-command.builder.js'
import {
  TargetAction
} from '../../../../order-management/accepted-transport-order/enums/target-action.enum.js'
import {
  OptimizePlanningOrderAction
} from '../../../optimisation/optimize-planning-order-action.enum.js'
import { exhaustiveCheck } from '../../../../../utils/helpers/exhaustive-check.helper.js'
import {
  RequestOptimisationTimeWindowCommandBuilder
} from '../../../optimisation/request-optimisation-command-builder/request-optimisation-time-window-command.builder.js'

import {
  RequestOptimisationCombinableOrdersCommandBuilder
} from '../../../optimisation/request-optimisation-command-builder/request-optimisation-combinable-orders-command.builder.js'
import {
  RequestOptimisationVehicleCommandBuilder
} from '../../../optimisation/request-optimisation-command-builder/request-optimisation-vehicle-command.builder.js'
import { Location } from '../../../../../modules/location/entities/location.entity.js'
import {
  RequestOptimisationLocationCommandBuilder
} from '../../../optimisation/request-optimisation-command-builder/request-optimisation-location-command.builder.js'
import {
  MaxTimeInVehicleFormula
} from '../../../../order-management/max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'

const DEFAULT_COMBINABLE_PRIORITY = 0

const DEFAULT_SHIFT_COST = 0
const VIRTUAL_SHIFT_COST = 10000

@Injectable()
export class RequestSimulationCommandFactory {
  private shifts: SimulatedShift[]

  create (
    simulation: Simulation,
    shifts: SimulatedShift[],
    orders: SimulatedTransportOrder[],
    formulas: MaxTimeInVehicleFormula[]
  ): RequestOptimisationCommand {
    this.shifts = shifts

    return new RequestOptimisationCommandBuilder()
      .withMaxTimeInVehicleFormulae(this.createFormulas(formulas))
      .withOrders(this.createOrders(orders))
      .withFleet(shifts.map(shift => this.createVehicle(shift)))
      .withConfig(this.createConfig(simulation))
      .withLocations(this.createLocations(this.collectLocations(shifts, orders)))
      .build()
  }

  private createFormulas (
    formulas: MaxTimeInVehicleFormula[]
  ): RequestOptimisationFormulaCommand[] {
    return formulas.map(formula =>
      new RequestOptimisationFormulaCommandBuilder()
        .withId(formula.uuid)
        .withFlatAddedSeconds(formula.flatAddedSeconds)
        .withTravelTimeCoefficient(formula.travelTimeCoefficient)
        .build()
    )
  }

  private createConfig (simulation: Simulation): RequestOptimisationConfigCommand {
    return new RequestOptimisationConfigCommandBuilder()
      .withSecondsPerIteration(simulation.config.secondsPerIteration)
      .withAverageOrdersPerVehicle(simulation.config.averageOrdersPerShift)
      .withTravelTimeFactor(1 + simulation.config.travelTimePenaltyFactor)
      .withMaxTimeInVehicleFactor(1 + simulation.config.maxTimeInVehicleMargin)
      .withArrivalWindowMargin(1 + simulation.config.arrivalWindowMargin)
      .build()
  }

  private createOrders (
    orders: SimulatedTransportOrder[]
  ): RequestOptimisationOrdersCommand {
    const nonCombinableOrders = orders.filter(order => this.isNonCombinable(order))
    const combinableOrders = orders.filter(order => !this.isNonCombinable(order))

    return new RequestOptimisationOrdersCommandBuilder()
      .withUncombinableOrders(nonCombinableOrders.map(order => this.createOrder(order)))
      .withCombinableOrders(this.createCombinableOrders(combinableOrders))
      .build()
  }

  private createOrder (
    order: SimulatedTransportOrder
  ): RequestOptimisationOrderCommand {
    assert(order.pickupLocation !== undefined, 'Pickup location not loaded')
    assert(order.dropOffLocation !== undefined, 'Drop off location not loaded')
    const timeWindow = this.createTimeWindow(order.arrivalWindowFrom, order.arrivalWindowUntil)
    const preferredShiftsUuids = this.collectPreferredDriverShiftUuids(order)

    return new RequestOptimisationOrderCommandBuilder()
      .withId(order.uuid)
      .withAction(this.mapOrderAction(order.targetAction))
      .withActionTimeWindow(timeWindow)
      .withMaxTimeInVehicleFormulaId(order.maxTimeInVehicleFormulaUuid)
      .withSecondsSpentAtPickup(order.pickupServiceTime.to('s').value)
      .withSecondsSpentAtDropOff(order.dropOffServiceTime.to('s').value)
      .withCapacityDemand(order.seatsDemand.totalDemand())
      .withPickupLocationId(order.pickupLocationUuid)
      .withDropOffLocationId(order.dropOffLocationUuid)
      .withDesignatedVehicleIds(preferredShiftsUuids)
      .build()
  }

  private collectPreferredDriverShiftUuids (order: SimulatedTransportOrder): string[] {
    assert(order.preferredDrivers !== undefined, 'Preferred drivers not loaded')
    return order.preferredDrivers
      .flatMap(driverPreference => this.getShiftsUuidsOfDriver(driverPreference.driverUuid))
  }

  private getShiftsUuidsOfDriver (driverUuid: string): string[] {
    return this.shifts
      .filter(shift => shift.driverUuid === driverUuid)
      .map(shift => shift.uuid)
  }

  private isNonCombinable (order: SimulatedTransportOrder): boolean {
    return order.contract?.contractType?.combinationRule === CombinationRule.PROHIBITED
  }

  private mapOrderAction (action: TargetAction): OptimizePlanningOrderAction {
    switch (action) {
      case TargetAction.DROP_OFF: return OptimizePlanningOrderAction.DROP_OFF
      case TargetAction.PICKUP: return OptimizePlanningOrderAction.PICKUP
      default: return exhaustiveCheck(action)
    }
  }

  private createTimeWindow (from: Date, until: Date): RequestOptimisationTimeWindowCommand {
    return new RequestOptimisationTimeWindowCommandBuilder()
      .withFrom(from)
      .withUntil(until)
      .build()
  }

  private createCombinableOrders (
    combinableOrders: SimulatedTransportOrder[]
  ): RequestOptimisationCombinableOrdersCommand[] {
    const codeOrders = new Map<string, SimulatedTransportOrder[]>()
    const clientOrders = new Map<string, SimulatedTransportOrder[]>()
    const remainingOrders: SimulatedTransportOrder[] = []

    for (const combinableOrder of combinableOrders) {
      assert(combinableOrder.contract?.contractType !== undefined, 'contract type not loaded')
      const combinationRule = combinableOrder.contract?.contractType.combinationRule
      if (combinationRule === CombinationRule.SAME_CONTRACT_CODE_ONLY) {
        const contractName = combinableOrder.contract?.contractType.name
        const orders = codeOrders.get(contractName) ?? []
        orders.push(combinableOrder)
        codeOrders.set(contractName, orders)
      } else if (combinationRule === CombinationRule.SAME_CUSTOMER_ONLY) {
        const clientId = combinableOrder.clientId
        const orders = clientOrders.get(clientId.hash) ?? []
        orders.push(combinableOrder)
        clientOrders.set(clientId.hash, orders)
      } else {
        remainingOrders.push(combinableOrder)
      }
    }

    const codeOrderCommands = Array.from(codeOrders.values())
      .map((group) => {
        assert(group[0].contract?.contractType !== undefined, 'contract type not loaded')
        return this.createCombinableOrdersGroup(group, group[0].contract?.contractType.priority)
      })

    const clientOrderCommands = Array.from(clientOrders.values())
      .map((group) => {
        return this.createCombinableOrdersGroup(group, DEFAULT_COMBINABLE_PRIORITY)
      })

    const result = [...codeOrderCommands, ...clientOrderCommands]

    if (remainingOrders.length > 0) {
      result.push(this.createCombinableOrdersGroup(remainingOrders, DEFAULT_COMBINABLE_PRIORITY))
    }

    return result
  }

  private createCombinableOrdersGroup (
    orders: SimulatedTransportOrder[],
    priority: number
  ): RequestOptimisationCombinableOrdersCommand {
    return new RequestOptimisationCombinableOrdersCommandBuilder()
      .withPriority(priority)
      .withOrders(orders.map(order => this.createOrder(order)))
      .build()
  }

  private createVehicle (shift: SimulatedShift): RequestOptimisationVehicleCommand {
    assert(shift.startLocation !== undefined, 'Start location not loaded')
    assert(shift.stopLocation !== undefined, 'Stop location not loaded')

    return new RequestOptimisationVehicleCommandBuilder()
      .withId(shift.uuid)
      .withCost(shift.isVirtual ? VIRTUAL_SHIFT_COST : DEFAULT_SHIFT_COST)
      .withCapacity(shift.capacity)
      .withMaxSecondsSpent(shift.maxDuration.to('s').value)
      .withStartLocationId(shift.startLocationUuid)
      .withStopLocationId(shift.stopLocationUuid)
      .withOperationWindow(this.createTimeWindow(shift.from, shift.until))
      .build()
  }

  private collectLocations (
    shifts: SimulatedShift[],
    orders: SimulatedTransportOrder[]
  ): Location[] {
    const locations = new Map<string, Location>()

    for (const shift of shifts) {
      assert(shift.startLocation != null, 'Start location not loaded')
      assert(shift.stopLocation != null, 'Stop location not loaded')
      locations.set(shift.startLocation.uuid, shift.startLocation)
      locations.set(shift.stopLocation.uuid, shift.stopLocation)
    }

    for (const order of orders) {
      assert(order.pickupLocation != null, 'Pickup location not loaded')
      assert(order.dropOffLocation != null, 'Drop off location not loaded')
      locations.set(order.pickupLocation.uuid, order.pickupLocation)
      locations.set(order.dropOffLocation.uuid, order.dropOffLocation)
    }

    return Array.from(locations.values())
  }

  private createLocations (locations: Location[]): RequestOptimisationLocationCommand[] {
    return locations.map((location) => {
      assert(location.address.coordinates != null, 'Location coordinates cannot be null')
      return new RequestOptimisationLocationCommandBuilder()
        .withId(location.uuid)
        .withCoordinates(location.address.coordinates)
        .build()
    })
  }
}
