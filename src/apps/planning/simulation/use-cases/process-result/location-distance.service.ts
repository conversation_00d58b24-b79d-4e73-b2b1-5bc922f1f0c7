import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { OsrmClient } from '../../../../../modules/osrm/osrm-client.js'
import { Location } from '../../../../../modules/location/entities/location.entity.js'
import { Distance } from '../../../../../utils/quantities/distance.js'
import { LocationUuid } from '../../../../../modules/location/entities/location.uuid.js'

@Injectable()
export class LocationDistanceService {
  private readonly locationCache: Map<string, Location>
  private readonly distanceCache: Map<string, Distance>

  constructor (
    private readonly osrmClient: OsrmClient,
    @InjectRepository(Location) private readonly locationRepository: Repository<Location>
  ) {
    this.locationCache = new Map<string, Location>()
    this.distanceCache = new Map<string, Distance>()
  }

  async distanceBetween (
    fromLocationUuid: LocationUuid,
    toLocationUuid: LocationUuid
  ): Promise<Distance> {
    const [from, to] = await Promise.all([
      this.fetchLocation(fromLocationUuid),
      this.fetchLocation(toLocationUuid)
    ])
    return await this.fetchDistance(from, to)
  }

  private async fetchLocation (locationUuid: LocationUuid): Promise<Location> {
    const cachedLocation = this.locationCache.get(locationUuid)

    if (cachedLocation !== undefined) {
      return cachedLocation
    }

    const location = await this.locationRepository.findOneOrFail({
      where: { uuid: locationUuid },
      select: { uuid: true, address: { coordinates: { longitude: true, latitude: true } } }
    })

    this.locationCache.set(location.uuid, location)
    return location
  }

  private async fetchDistance (from: Location, to: Location): Promise<Distance> {
    const distanceCacheKey = this.createDistanceKey(from, to)
    const cachedDistance = this.distanceCache.get(distanceCacheKey)

    if (cachedDistance !== undefined) {
      return cachedDistance
    }
    const distance = await this.calculateDistance(from, to)

    this.distanceCache.set(distanceCacheKey, distance)
    return distance
  }

  private async calculateDistance (from: Location, to: Location): Promise<Distance> {
    assert(from.address.coordinates != null && to.address.coordinates != null, 'Coordinates cannot be null')
    if (from.address.coordinates.equals(to.address.coordinates)) {
      return new Distance(0, 'm')
    }

    const route = await this.osrmClient.calculateRoute(
      from.address.coordinates,
      to.address.coordinates
    )
    return new Distance(Math.round(route.distance), 'm')
  }

  private createDistanceKey (from: Location, to: Location): string {
    return `${from.uuid}-${to.uuid}`
  }
}
