import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'
import dayjs from 'dayjs'
import {
  AcceptedTransportOrderEntityBuilder
} from '../../../../../order-management/accepted-transport-order/builders/accepted-transport-order.entity.builder.js'
import {
  AcceptedTransportOrder
} from '../../../../../order-management/accepted-transport-order/entities/accepted-transport-order.entity.js'
import {
  OrganizationEntityBuilder
} from '../../../../../order-management/organization/builders/organization.entity.builder.js'
import {
  ContractTypeEntityBuilder
} from '../../../../../order-management/contract-type/builders/contract-type.entity.builder.js'
import {
  CareUserEntityBuilder
} from '../../../../../order-management/care-user/builders/care-user.entity.builder.js'
import { CareUser } from '../../../../../order-management/care-user/entities/care-user.entity.js'
import {
  ShiftEntityBuilder
} from '../../../../../resource-management/shift/builders/shift.entity.builder.js'
import { Shift } from '../../../../../resource-management/shift/entities/shift.entity.js'
import {
  Organization
} from '../../../../../order-management/organization/entities/organization.entity.js'
import {
  ContractType
} from '../../../../../order-management/contract-type/entities/contract-type.entity.js'
import { Branch } from '../../../../../resource-management/branch/branch.entity.js'
import {
  BranchEntityBuilder
} from '../../../../../resource-management/branch/builders/branch.entity.builder.js'
import {
  DriverEntityBuilder
} from '../../../../../resource-management/driver/entities/driver/driver.entity.builder.js'
import { Driver } from '../../../../../resource-management/driver/entities/driver/driver.entity.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import {
  AcceptedTransportOrderPreferredDriverBuilder
} from '../../../../../order-management/accepted-transport-order/builders/accepted-transport-order-preferred-driver.builder.js'
import {
  AcceptedTransportOrderPreferredDriver
} from '../../../../../order-management/accepted-transport-order/entities/accepted-transport-order-preferred-driver.entity.js'
import {
  MaxTimeInVehicleFormulaEntityBuilder
} from '../../../../../order-management/max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.builder.js'
import {
  MaxTimeInVehicleFormula
} from '../../../../../order-management/max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'

import { ClientId } from '../../../../../order-management/client/client-id.js'
import { ClientType } from '../../../../../order-management/client/client-type.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../../resource-management/driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../../resource-management/driver-default-shift/entities/driver-default-shift.entity.js'
import { ContractEntityBuilder } from '../../../../../order-management/contract/entities/contract.entity.builder.js'
import { Contract } from '../../../../../order-management/contract/entities/contract.entity.js'
import { VIRTUAL_SHIFT_LOCATION_UUID } from '../create-simulation.use-case.js'
import { PricingFormula } from '../../../../../pricing/pricing-formula/entities/pricing-formula.entity.js'
import { PricingFormulaEntityBuilder } from '../../../../../pricing/pricing-formula/pricing-formula.entity.builder.js'
import { BranchUuid } from '../../../../../resource-management/branch/branch.uuid.js'
import { DriverUuid, generateDriverUuid } from '../../../../../resource-management/driver/entities/driver/driver.uuid.js'

export interface CreateSimulationTestContext {
  date: string
  shifts: Shift[]
  orders: AcceptedTransportOrder[]
}

export async function setupCreateSimulationTestContext (
  entityManager: EntityManager
): Promise<CreateSimulationTestContext> {
  const date = '1970-01-01'
  const driverUuid = generateDriverUuid()
  const shift = await createShiftFixture(entityManager, date, driverUuid)
  const acceptedTransportOrder = await setupAcceptedTransportOrder(entityManager, date, driverUuid)

  return {
    date,
    shifts: [shift],
    orders: [acceptedTransportOrder]
  }
}

export async function createShiftFixture (
  entityManager: EntityManager,
  date: string,
  driverUuid: DriverUuid
): Promise<Shift> {
  const branch = new BranchEntityBuilder()
    .withUuid('931350cb-c804-5860-9f49-1a4083fb698a' as BranchUuid)
    .build()
  const location = new LocationBuilder().build()
  const virtualLocation = new LocationBuilder().withUuid(VIRTUAL_SHIFT_LOCATION_UUID).build()

  const defaultShift = new DriverDefaultShiftEntityBuilder()
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const driver = new DriverEntityBuilder()
    .withUuid(driverUuid)
    .withBranchUuid(branch.uuid)
    .withDefaultShiftUuid(defaultShift.uuid)
    .build()

  const existingShift = new ShiftEntityBuilder()
    .withFrom(dayjs(date).set('hour', 6).toDate())
    .withUntil(dayjs(date).set('hour', 20).toDate())
    .withDriverUuid(driver.uuid)
    .withBranchUuid(branch.uuid)
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  await entityManager.insert(Branch, branch)
  await entityManager.insert(Location, [location, virtualLocation])
  await entityManager.insert(DriverDefaultShift, defaultShift)
  await entityManager.insert(Driver, driver)
  await entityManager.insert(Shift, existingShift)
  return existingShift
}

async function setupAcceptedTransportOrder (
  entityManager: EntityManager,
  date: string,
  driverUuid: string
): Promise<AcceptedTransportOrder> {
  const timeInVehicleFormula = new MaxTimeInVehicleFormulaEntityBuilder()
    .build()
  await entityManager.insert(MaxTimeInVehicleFormula, timeInVehicleFormula)

  const pricingFormula = new PricingFormulaEntityBuilder().build()
  await entityManager.insert(PricingFormula, pricingFormula)

  const contractType = new ContractTypeEntityBuilder()
    .withMaxTimeInVehicleFormulaUuid(timeInVehicleFormula.uuid)
    .withPricingFormulaUuid(pricingFormula.uuid)
    .build()

  const organization = new OrganizationEntityBuilder()
    .build()

  const careUser = new CareUserEntityBuilder()
    .build()

  const location = new LocationBuilder().build()

  const contract = new ContractEntityBuilder()
    .withContractTypeUuid(contractType.uuid)
    .withClientId(new ClientId(careUser.uuid, ClientType.CARE_USER))
    .build()

  const acceptedTransportOrder = new AcceptedTransportOrderEntityBuilder()
    .withDate(date)
    .withClientId(new ClientId(careUser.uuid, ClientType.CARE_USER))
    .withContractUuid(contract.uuid)
    .withCareUserUuid(careUser.uuid)
    .withPickupLocationUuid(location.uuid)
    .withDropOffLocationUuid(location.uuid)
    .withFormulaUuid(timeInVehicleFormula.uuid)
    .build()

  const preferredDriver = new AcceptedTransportOrderPreferredDriverBuilder()
    .withOrderUuid(acceptedTransportOrder.uuid)
    .withDriverUuid(driverUuid)
    .build()

  await Promise.all([
    entityManager.insert(ContractType, contractType),
    entityManager.insert(Location, location)
  ])
  await entityManager.insert(Organization, organization)
  await entityManager.insert(CareUser, careUser)
  await entityManager.insert(Contract, contract)
  await entityManager.insert(AcceptedTransportOrder, acceptedTransportOrder)
  await entityManager.insert(AcceptedTransportOrderPreferredDriver, preferredDriver)

  acceptedTransportOrder.careUser = careUser
  acceptedTransportOrder.contract = contract
  acceptedTransportOrder.contractUuid = contract.uuid
  return acceptedTransportOrder
}
