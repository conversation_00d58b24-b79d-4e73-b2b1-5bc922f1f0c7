import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'
import dayjs from 'dayjs'
import { Simulation } from '../../../entities/simulation.entity.js'
import { Branch } from '../../../../../resource-management/branch/branch.entity.js'
import { BranchEntityBuilder } from '../../../../../resource-management/branch/builders/branch.entity.builder.js'
import { DriverEntityBuilder } from '../../../../../resource-management/driver/entities/driver/driver.entity.builder.js'
import { Driver } from '../../../../../resource-management/driver/entities/driver/driver.entity.js'
import { ShiftEntityBuilder } from '../../../../../resource-management/shift/builders/shift.entity.builder.js'
import { Shift } from '../../../../../resource-management/shift/entities/shift.entity.js'
import { SimulationStatus } from '../../../enums/simulation-status.enum.js'
import { SimulatedPlanningSequenceEntityBuilder } from '../../../entity-builders/simulated-planning-sequence-entity.builder.js'
import { SimulatedPlanningSequence } from '../../../entities/simulated-planning-sequence.entity.js'
import { SimulatedShiftBuilder } from '../../../entity-builders/simulated-shift.builder.js'
import { SimulatedShift } from '../../../entities/simulated-shift.entity.js'
import { SimulationBuilder } from '../../../entity-builders/simulation.builder.js'
import { DateFormats } from '../../../../../../utils/dates/date-formats.js'
import { CareUserEntityBuilder } from '../../../../../order-management/care-user/builders/care-user.entity.builder.js'
import { CareUser } from '../../../../../order-management/care-user/entities/care-user.entity.js'
import { ContractType } from '../../../../../order-management/contract-type/entities/contract-type.entity.js'
import { AcceptedTransportOrderEntityBuilder } from '../../../../../order-management/accepted-transport-order/builders/accepted-transport-order.entity.builder.js'
import { AcceptedTransportOrder } from '../../../../../order-management/accepted-transport-order/entities/accepted-transport-order.entity.js'
import { OrganizationEntityBuilder } from '../../../../../order-management/organization/builders/organization.entity.builder.js'
import { ContractTypeEntityBuilder } from '../../../../../order-management/contract-type/builders/contract-type.entity.builder.js'
import { ContractTypeAbbreviation } from '../../../../../order-management/contract-type/enums/contract-type-abbreviation.enum.js'
import { SimulatedPlanningAction } from '../../../entities/simulated-planning-action.entity.js'
import { SimulatedTransportOrder } from '../../../entities/simulated-transport-order.entity.js'
import { SimulatedPlanningActionEntityBuilder } from '../../../entity-builders/simulated-planning-action-entity.builder.js'
import { SimulatedTransportOrderBuilder } from '../../../entity-builders/simulated-transport-order.builder.js'
import { SimulatedPlanningActionType } from '../../../enums/simulated-planning-action-type.js'
import { Organization } from '../../../../../order-management/organization/entities/organization.entity.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { MaxTimeInVehicleFormula } from '../../../../../order-management/max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { MaxTimeInVehicleFormulaEntityBuilder } from '../../../../../order-management/max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.builder.js'
import { ClientId } from '../../../../../order-management/client/client-id.js'
import { ClientType } from '../../../../../order-management/client/client-type.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../../resource-management/driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../../resource-management/driver-default-shift/entities/driver-default-shift.entity.js'
import { DepartmentEntityBuilder } from '../../../../../order-management/department/entities/department.entity.builder.js'
import { Department } from '../../../../../order-management/department/entities/department.entity.js'
import { ContractTypeName } from '../../../../../order-management/contract-type/enums/contract-type-name.enum.js'
import { ContractEntityBuilder } from '../../../../../order-management/contract/entities/contract.entity.builder.js'
import { Contract } from '../../../../../order-management/contract/entities/contract.entity.js'
import { UserEntityBuilder } from '../../../../../../app/users/tests/user-entity.builder.js'
import { User } from '../../../../../../app/users/entities/user.entity.js'
import { PricingFormula } from '../../../../../pricing/pricing-formula/entities/pricing-formula.entity.js'
import { PricingFormulaEntityBuilder } from '../../../../../pricing/pricing-formula/pricing-formula.entity.builder.js'

export interface GetPlanningIndexTestContext {
  simulation: Simulation
  simulatedShifts: SimulatedShift[]
  usedSimulatedShifts: SimulatedShift[]
  user: User
  branch: Branch
  location: Location
  drivers: Driver[]
}

export async function setupGetSimulatedShiftsTestContext (
  entityManager: EntityManager
): Promise<GetPlanningIndexTestContext> {
  const user = new UserEntityBuilder().build()

  const location = new LocationBuilder().build()

  const branch = new BranchEntityBuilder()
    .build()

  const organization = new OrganizationEntityBuilder()
    .withAbbreviation('WSM')
    .build()

  const department = new DepartmentEntityBuilder()
    .withOrganizationUuid(organization.uuid)
    .build()

  const defaultShift1 = new DriverDefaultShiftEntityBuilder()
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const driver = new DriverEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withFirstName('aa')
    .withLastName('aa')
    .withDefaultShiftUuid(defaultShift1.uuid)
    .build()

  const defaultShift2 = new DriverDefaultShiftEntityBuilder()
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const driver2 = new DriverEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withFirstName('bb')
    .withLastName('bb')
    .withDefaultShiftUuid(defaultShift2.uuid)
    .build()

  const timeInVehicleFormula = new MaxTimeInVehicleFormulaEntityBuilder()
    .build()
  await entityManager.insert(MaxTimeInVehicleFormula, timeInVehicleFormula)

  const pricingFormula = new PricingFormulaEntityBuilder().build()
  await entityManager.insert(PricingFormula, pricingFormula)

  const contractTypeDay = new ContractTypeEntityBuilder()
    .withName(ContractTypeName.DAY)
    .withAbbreviation(ContractTypeAbbreviation.D)
    .withMaxTimeInVehicleFormulaUuid(timeInVehicleFormula.uuid)
    .withPricingFormulaUuid(pricingFormula.uuid)
    .build()

  const contractTypeMav = new ContractTypeEntityBuilder()
    .withName(ContractTypeName.MAV)
    .withAbbreviation(ContractTypeAbbreviation.K)
    .withPriority(2)
    .withMaxTimeInVehicleFormulaUuid(timeInVehicleFormula.uuid)
    .withPricingFormulaUuid(pricingFormula.uuid)
    .build()

  const shift = new ShiftEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withDriverUuid(driver.uuid)
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const shift2 = new ShiftEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withDriverUuid(driver2.uuid)
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const simulation = new SimulationBuilder()
    .forDate(dayjs().format(DateFormats.POSTGRES_DATE))
    .withStatus(SimulationStatus.PENDING)
    .withAverageOrdersPerShift(5)
    .withMaxTimeInVehicleMargin(10)
    .withPickupAndDropOffWindowMargin(10)
    .withSecondsPerIteration(5)
    .withTravelTimeMargin(5)
    .withAvailableDrivers(1)
    .withOrdersInSimulation(1)
    .withCreatorUuid(user.uuid)
    .build()

  const simulatedShift = new SimulatedShiftBuilder()
    .withFrom(dayjs().toDate())
    .withUntil(dayjs().toDate())
    .withlicensePlate('1-ABC-123')
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .withDriverUuid(driver.uuid)
    .withBranchUuid(branch.uuid)
    .withSimulationUuid(simulation.uuid)
    .withOriginalShiftUuid(shift.uuid)
    .withIsUsed(true)
    .build()

  const simulatedShift2 = new SimulatedShiftBuilder()
    .withFrom(dayjs().toDate())
    .withUntil(dayjs().toDate())
    .withlicensePlate('1-ABC-123')
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .withDriverUuid(driver2.uuid)
    .withBranchUuid(branch.uuid)
    .withSimulationUuid(simulation.uuid)
    .withOriginalShiftUuid(shift2.uuid)
    .withIsUsed(true)
    .build()

  const simulatedShift3 = new SimulatedShiftBuilder()
    .withFrom(dayjs().toDate())
    .withUntil(dayjs().toDate())
    .withlicensePlate('1-ABC-123')
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .withDriverUuid(driver2.uuid)
    .withBranchUuid(branch.uuid)
    .withSimulationUuid(simulation.uuid)
    .withOriginalShiftUuid(shift2.uuid)
    .withIsVirtual(true)
    .withIsUsed(true)
    .build()

  const simulatedShift4 = new SimulatedShiftBuilder()
    .withFrom(dayjs().toDate())
    .withUntil(dayjs().toDate())
    .withlicensePlate('1-ABC-123')
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .withDriverUuid(driver2.uuid)
    .withBranchUuid(branch.uuid)
    .withSimulationUuid(simulation.uuid)
    .withOriginalShiftUuid(shift2.uuid)
    .withIsVirtual(true)
    .withIsUsed(false)
    .build()

  const simulatedShift5 = new SimulatedShiftBuilder()
    .withFrom(dayjs().toDate())
    .withUntil(dayjs().toDate())
    .withlicensePlate('1-ABC-123')
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .withDriverUuid(driver2.uuid)
    .withBranchUuid(branch.uuid)
    .withSimulationUuid(simulation.uuid)
    .withOriginalShiftUuid(shift2.uuid)
    .withIsVirtual(true)
    .withIsUsed(false)
    .build()

  const planningSequences: SimulatedPlanningSequence[] = []
  planningSequences.push(new SimulatedPlanningSequenceEntityBuilder()
    .withSimulatedShiftUuid(simulatedShift.uuid)
    .build()
  )
  planningSequences.push(new SimulatedPlanningSequenceEntityBuilder()
    .withSimulatedShiftUuid(simulatedShift.uuid)
    .withStartsAt(dayjs().add(1, 'hour').toDate())
    .withEndsAt(dayjs().add(4, 'hour').toDate())
    .withFirstCareUserPickupAt(dayjs().add(2, 'hour').toDate())
    .withClientId(new ClientId(department.uuid, ClientType.ORGANIZATION))
    .build()
  )

  const planningSequences2: SimulatedPlanningSequence[] = []
  planningSequences2.push(new SimulatedPlanningSequenceEntityBuilder()
    .withSimulatedShiftUuid(simulatedShift2.uuid)
    .build()
  )
  planningSequences2.push(new SimulatedPlanningSequenceEntityBuilder()
    .withSimulatedShiftUuid(simulatedShift2.uuid)
    .withStartsAt(dayjs().add(1, 'hour').toDate())
    .withEndsAt(dayjs().add(4, 'hour').toDate())
    .withFirstCareUserPickupAt(dayjs().add(2, 'hour').toDate())
    .build()
  )

  const planningSequences3: SimulatedPlanningSequence[] = []
  planningSequences3.push(new SimulatedPlanningSequenceEntityBuilder()
    .withSimulatedShiftUuid(simulatedShift3.uuid)
    .build()
  )
  planningSequences3.push(new SimulatedPlanningSequenceEntityBuilder()
    .withSimulatedShiftUuid(simulatedShift3.uuid)
    .withStartsAt(dayjs().add(1, 'hour').toDate())
    .withEndsAt(dayjs().add(4, 'hour').toDate())
    .withFirstCareUserPickupAt(dayjs().add(2, 'hour').toDate())
    .build()
  )

  const careUser = new CareUserEntityBuilder()
    .build()

  const contractDay = new ContractEntityBuilder()
    .withContractTypeUuid(contractTypeDay.uuid)
    .withClientId(new ClientId(careUser.uuid, ClientType.CARE_USER))
    .build()

  const contractMav = new ContractEntityBuilder()
    .withContractTypeUuid(contractTypeMav.uuid)
    .withClientId(new ClientId(careUser.uuid, ClientType.CARE_USER))
    .build()

  const order = new AcceptedTransportOrderEntityBuilder()
    .withClientId(new ClientId(careUser.uuid, ClientType.CARE_USER))
    .withCareUserUuid(careUser.uuid)
    .withContractUuid(contractDay.uuid)
    .withPickupLocationUuid(location.uuid)
    .withDropOffLocationUuid(location.uuid)
    .withFormulaUuid(timeInVehicleFormula.uuid)
    .build()

  const simulatedOrder = new SimulatedTransportOrderBuilder()
    .withSimulationUuid(simulation.uuid)
    .withOriginalOrderUuid(order.uuid)
    .withId(order.id)
    .withDate(order.date)
    .withDescription(order.description)
    .withTargetAction(order.targetAction)
    .withTargetTime(order.targetTime)
    .withArrivalWindowFrom(order.arrivalWindowFrom)
    .withArrivalWindowUntil(order.arrivalWindowUntil)
    .withDemandNormalSeats(order.seatsDemand.normalSeats)
    .withDemandWheelChairSeats(order.seatsDemand.wheelChairSeats)
    .withDemandChildSeats(order.seatsDemand.childSeats)
    .withPickupLocationUuid(order.pickupLocationUuid)
    .withPickupServiceTime(order.pickupServiceTime)
    .withDropOffLocationUuid(order.dropOffLocationUuid)
    .withDropOffServiceTime(order.dropOffServiceTime)
    .withClientId(order.clientId)
    .withCareUserUuid(order.careUserUuid)
    .withContractUuid(order.contractUuid)
    .withFormulaUuid(timeInVehicleFormula.uuid)
    .build()

  const simulatedOrder2 = new SimulatedTransportOrderBuilder()
    .withSimulationUuid(simulation.uuid)
    .withOriginalOrderUuid(order.uuid)
    .withId(order.id)
    .withDate(order.date)
    .withDescription(order.description)
    .withTargetAction(order.targetAction)
    .withTargetTime(order.targetTime)
    .withArrivalWindowFrom(order.arrivalWindowFrom)
    .withArrivalWindowUntil(order.arrivalWindowUntil)
    .withDemandNormalSeats(order.seatsDemand.normalSeats)
    .withDemandWheelChairSeats(order.seatsDemand.wheelChairSeats)
    .withDemandChildSeats(order.seatsDemand.childSeats)
    .withPickupLocationUuid(order.pickupLocationUuid)
    .withPickupServiceTime(order.pickupServiceTime)
    .withDropOffLocationUuid(order.dropOffLocationUuid)
    .withDropOffServiceTime(order.dropOffServiceTime)
    .withClientId(order.clientId)
    .withCareUserUuid(order.careUserUuid)
    .withContractUuid(contractMav.uuid)
    .withFormulaUuid(timeInVehicleFormula.uuid)
    .build()

  const planningActions: SimulatedPlanningAction[] = []

  planningActions.push(new SimulatedPlanningActionEntityBuilder()
    .withActionType(SimulatedPlanningActionType.DRIVE)
    .withSimulatedOrderUuid(simulatedOrder.uuid)
    .withSimulatedPlanningSequenceUuid(planningSequences[0].uuid)
    .withFromLocationUuid(location.uuid)
    .withToLocationUuid(location.uuid)
    .build()
  )

  planningActions.push(new SimulatedPlanningActionEntityBuilder()
    .withActionType(SimulatedPlanningActionType.DRIVE)
    .withSimulatedOrderUuid(simulatedOrder2.uuid)
    .withSimulatedPlanningSequenceUuid(planningSequences[1].uuid)
    .withFromLocationUuid(location.uuid)
    .withToLocationUuid(location.uuid)
    .build()
  )

  planningActions.push(new SimulatedPlanningActionEntityBuilder()
    .withActionType(SimulatedPlanningActionType.DRIVE)
    .withSimulatedOrderUuid(simulatedOrder.uuid)
    .withSimulatedPlanningSequenceUuid(planningSequences[1].uuid)
    .withFromLocationUuid(location.uuid)
    .withToLocationUuid(location.uuid)
    .build()
  )

  planningActions.push(new SimulatedPlanningActionEntityBuilder()
    .withActionType(SimulatedPlanningActionType.DRIVE)
    .withSimulatedOrderUuid(simulatedOrder2.uuid)
    .withSimulatedPlanningSequenceUuid(planningSequences[1].uuid)
    .withFromLocationUuid(location.uuid)
    .withToLocationUuid(location.uuid)
    .build()
  )

  await Promise.all([
    entityManager.insert(User, user),
    entityManager.insert(Location, location),
    entityManager.insert(Branch, branch),
    entityManager.insert(ContractType, [contractTypeDay, contractTypeMav])
  ])

  await entityManager.insert(DriverDefaultShift, [defaultShift1, defaultShift2])
  await entityManager.insert(Driver, [driver, driver2])
  await entityManager.insert(Shift, [shift, shift2])
  await entityManager.insert(Simulation, simulation)
  const simulatedShifts = [
    simulatedShift,
    simulatedShift2,
    simulatedShift3,
    simulatedShift4,
    simulatedShift5
  ]
  await entityManager.insert(SimulatedShift, simulatedShifts)
  await entityManager.insert(SimulatedPlanningSequence, [
    ...planningSequences,
    ...planningSequences2,
    ...planningSequences3
  ])

  await entityManager.insert(Organization, organization)
  await entityManager.insert(Department, department)
  await entityManager.insert(CareUser, careUser)
  await entityManager.insert(Contract, [contractDay, contractMav])
  await entityManager.insert(AcceptedTransportOrder, order)
  await entityManager.insert(SimulatedTransportOrder, [simulatedOrder, simulatedOrder2])
  await entityManager.insert(SimulatedPlanningAction, planningActions)

  const usedSimulatedShifts = [simulatedShift, simulatedShift2, simulatedShift3]
  const drivers = [driver, driver2]
  return { simulation, simulatedShifts, user, branch, location, usedSimulatedShifts, drivers }
}
