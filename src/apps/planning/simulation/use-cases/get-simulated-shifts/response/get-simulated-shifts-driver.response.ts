import { ApiProperty } from '@nestjs/swagger'
import { Driver } from '../../../../../resource-management/driver/entities/driver/driver.entity.js'
import { tc } from '../../../../../../modules/localization/helpers/translate.helper.js'

export class GetSimulatedShiftsDriverResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string | null

  @ApiProperty({ type: String })
  firstName: string

  @ApiProperty({ type: String })
  lastName: string

  constructor (driver: Driver | null) {
    this.uuid = driver?.uuid ?? null
    this.firstName = driver?.firstName ?? tc('common.virtual_driver.firstName')
    this.lastName = driver?.lastName ?? tc('common.virtual_driver.lastName')
  }
}
