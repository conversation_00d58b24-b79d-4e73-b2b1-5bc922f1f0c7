import { Module } from '@nestjs/common'

import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Driver } from '../../../../resource-management/driver/entities/driver/driver.entity.js'
import { SimulatedPlanningSequence } from '../../entities/simulated-planning-sequence.entity.js'
import { Simulation } from '../../entities/simulation.entity.js'
import { SimulatedShift } from '../../entities/simulated-shift.entity.js'
import { TypesenseModule } from '../../../../../modules/typesense/typesense.module.js'
import { SimulatedPlanningAction } from '../../entities/simulated-planning-action.entity.js'
import { ClientModule } from '../../../../order-management/client/client-module.js'
import { GetSimulatedShiftsUseCase } from './get-simulated-shifts.use-case.js'
import { GetSimulatedShiftsController } from './get-simulated-shifts.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Simulation,
      SimulatedPlanningSequence,
      Driver,
      SimulatedShift,
      SimulatedPlanningAction
    ]),
    ClientModule,
    TypesenseModule
  ],
  controllers: [GetSimulatedShiftsController],
  providers: [
    GetSimulatedShiftsUseCase
  ]
})
export class GetSimulatedShiftsModule {}
