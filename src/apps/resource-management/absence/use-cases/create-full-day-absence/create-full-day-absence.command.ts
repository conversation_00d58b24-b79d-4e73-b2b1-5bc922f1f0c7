import { IsDateWithoutTimeString } from '@wisemen/validators'
import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsUUID } from 'class-validator'
import { AbsenceType, AbsenceTypeApiProperty } from '../../types/absence.type.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'

export class CreateFullDayAbsenceCommand {
  @ApiProperty({ type: 'string', format: 'uuid' })
  @IsUUID()
  driverUuid: DriverUuid

  @ApiProperty({ type: 'string', format: 'date' })
  @IsDateWithoutTimeString()
  date: string

  @AbsenceTypeApiProperty()
  @IsEnum(AbsenceType)
  type: AbsenceType
}
