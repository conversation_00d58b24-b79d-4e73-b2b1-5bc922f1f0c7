import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { FullDayAbsence } from '../../entities/full-day-absence.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'

@Injectable()
export class CreateFullDayAbsenceRepository {
  constructor (
    @InjectRepository(Driver) private readonly driverRepository: Repository<Driver>,
    @InjectRepository(FullDayAbsence) private readonly absenceRepository: Repository<FullDayAbsence>
  ) {}

  async driverExists (withUuid: DriverUuid): Promise<boolean> {
    return this.driverRepository.existsBy({ uuid: withUuid })
  }

  async otherFullDayAbsenceExists (forDriverUuid: string, onDate: string): Promise<boolean> {
    return this.absenceRepository.existsBy({
      driverUuid: forDriverUuid,
      date: onDate
    })
  }

  async insert (absence: FullDayAbsence): Promise<void> {
    await this.absenceRepository.insert(absence)
  }
}
