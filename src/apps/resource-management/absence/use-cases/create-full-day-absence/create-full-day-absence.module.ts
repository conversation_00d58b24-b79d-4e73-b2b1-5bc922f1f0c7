import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { FullDayAbsence } from '../../entities/full-day-absence.entity.js'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { CreateFullDayAbsenceRepository } from './create-full-day-absence.repository.js'
import { CreateFullDayAbsenceUseCase } from './create-full-day-absence.use-case.js'
import { CreateFullDayAbsenceController } from './create-full-day-absence.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([Driver, FullDayAbsence]),
    DomainEventEmitterModule
  ],
  controllers: [CreateFullDayAbsenceController],
  providers: [
    CreateFullDayAbsenceUseCase,
    CreateFullDayAbsenceRepository
  ]
})
export class CreateFullDayAbsenceModule {}
