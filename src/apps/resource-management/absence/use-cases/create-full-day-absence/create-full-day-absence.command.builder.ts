import { AbsenceType } from '../../types/absence.type.js'
import { DriverUuid, generateDriverUuid } from '../../../driver/entities/driver/driver.uuid.js'
import { CreateFullDayAbsenceCommand } from './create-full-day-absence.command.js'

export class CreateFullDayAbsenceCommandBuilder {
  private readonly command = new CreateFullDayAbsenceCommand()

  constructor () {
    this.command.date = '1970-01-01'
    this.command.driverUuid = generateDriverUuid()
    this.command.type = AbsenceType.OTHER
  }

  withDriverUuid (uuid: DriverUuid): this {
    this.command.driverUuid = uuid
    return this
  }

  withDate (date: string): this {
    this.command.date = date
    return this
  }

  withType (type: AbsenceType): this {
    this.command.type = type
    return this
  }

  build (): CreateFullDayAbsenceCommand {
    return this.command
  }
}
