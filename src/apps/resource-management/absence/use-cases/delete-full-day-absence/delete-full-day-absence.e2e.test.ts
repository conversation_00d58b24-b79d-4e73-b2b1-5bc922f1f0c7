import { after, before, describe, it } from 'node:test'
import supertest from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { BranchEntityBuilder } from '../../../branch/builders/branch.entity.builder.js'
import { LocationBuilder } from '../../../../../modules/location/location.builder.js'
import { DriverDefaultShiftEntityBuilder } from '../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverEntityBuilder } from '../../../driver/entities/driver/driver.entity.builder.js'
import { Branch } from '../../../branch/branch.entity.js'
import { DriverDefaultShift } from '../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { Location } from '../../../../../modules/location/entities/location.entity.js'
import { FullDayAbsenceEntityBuilder } from '../../builders/full-day-absence.entity.builder.js'
import { FullDayAbsence } from '../../entities/full-day-absence.entity.js'

describe('Delete full day absence e2e tests', () => {
  let setup: EndToEndTestSetup
  let token: string

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    token = (await setup.authContext.getAdminUser()).token
  })

  after(async () => await setup.teardown())

  it('deletes a full day absence', async () => {
    const branch = new BranchEntityBuilder().build()
    const location = new LocationBuilder().build()
    const defaultShift = new DriverDefaultShiftEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .build()
    const driver = new DriverEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withDefaultShiftUuid(defaultShift.uuid)
      .build()

    const absence = new FullDayAbsenceEntityBuilder()
      .withDriverUuid(driver.uuid)
      .build()

    await setup.dataSource.manager.insert(Branch, branch)
    await setup.dataSource.manager.insert(Location, location)
    await setup.dataSource.manager.insert(DriverDefaultShift, defaultShift)
    await setup.dataSource.manager.insert(Driver, driver)
    await setup.dataSource.manager.insert(FullDayAbsence, absence)

    const response = await supertest(setup.httpServer)
      .delete(`/full-day-absences/${absence.uuid}`)
      .set('Authorization', `Bearer ${token}`)

    expect(response).toHaveStatus(HttpStatus.NO_CONTENT)
  })
})
