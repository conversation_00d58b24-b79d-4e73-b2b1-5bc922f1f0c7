import { Injectable } from '@nestjs/common'
import { LessThanOrEqual, MoreThanOrEqual, Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Absence } from '../../entities/absence.entity.js'
import { Shift } from '../../../shift/entities/shift.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { FullDayAbsence } from '../../entities/full-day-absence.entity.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'

@Injectable()
export class CreateAbsenceRepository {
  constructor (
    @InjectRepository(Absence) private readonly absenceRepository: Repository<Absence>,
    @InjectRepository(Shift) private readonly shiftRepository: Repository<Shift>,
    @InjectRepository(Driver) private readonly driverRepository: Repository<Driver>,
    @InjectRepository(FullDayAbsence)
    private readonly fullDayAbsenceRepository: Repository<FullDayAbsence>
  ) {}

  async driverExists (uuid: DriverUuid): Promise<boolean> {
    return await this.driverRepository.existsBy({ uuid })
  }

  async overlappingAbsenceExists (absence: Absence): Promise<boolean> {
    return await this.absenceRepository.existsBy({
      driverUuid: absence.driverUuid,
      from: LessThanOrEqual(absence.until),
      until: MoreThanOrEqual(absence.from)
    })
  }

  async findOverlappingShifts (absence: Absence): Promise<Shift[]> {
    return await this.shiftRepository.findBy({
      driverUuid: absence.driverUuid,
      from: LessThanOrEqual(absence.until),
      until: MoreThanOrEqual(absence.from)
    })
  }

  async fullDayAbsenceExists (forDriverUuid: string, onDate: string): Promise<boolean> {
    return await this.fullDayAbsenceRepository.existsBy({
      driverUuid: forDriverUuid,
      date: onDate
    })
  }

  async insertAbsence (absence: Absence): Promise<void> {
    await this.absenceRepository.insert(absence)
  }

  async insertShifts (shifts: Shift[]): Promise<void> {
    await this.shiftRepository.insert(shifts)
  }

  async deleteShifts (shifts: Shift[]): Promise<void> {
    await this.shiftRepository.softRemove(shifts)
  }
}
