import { Injectable } from '@nestjs/common'
import dayjs from 'dayjs'
import { DataSource } from 'typeorm'
import { transaction } from '@wisemen/nestjs-typeorm'
import { Absence } from '../../entities/absence.entity.js'
import { AbsenceEntityBuilder } from '../../builders/absence.entity.builder.js'
import { AbsenceOverlapsWithAbsenceError } from '../../errors/absence-overlaps-with-absence.error.js'
import { DriverNotFoundError } from '../../../driver/errors/driver-not-found.error.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { ShiftSplitter } from '../../../shift/shift-splitter.js'
import { DateTimeRange } from '../../../../../utils/dates/date-ranges/date-time-range.js'
import { Shift } from '../../../shift/entities/shift.entity.js'
import { ShiftDeletedEvent } from '../../../shift/use-cases/delete-shift/shift-deleted.event.js'
import { ShiftCreatedEvent } from '../../../shift/use-cases/create-shift/shift-created.event.js'

import { DateFormats } from '../../../../../utils/dates/date-formats.js'
import {
  AbsenceOverlapsWithFullDayAbsenceError
} from '../../errors/absence-overlaps-with-full-day-absence.error.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'
import { CreateAbsenceCommand } from './create-absence.command.js'
import { CreateAbsenceResponse } from './create-absence.response.js'
import { CreateAbsenceRepository } from './create-absence.repository.js'
import { AbsenceCreatedEvent } from './absence-created.event.js'

@Injectable()
export class CreateAbsenceUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    private readonly repository: CreateAbsenceRepository
  ) {}

  public async execute (
    driverUuid: DriverUuid,
    command: CreateAbsenceCommand
  ): Promise<CreateAbsenceResponse> {
    if (!await this.repository.driverExists(driverUuid)) {
      throw new DriverNotFoundError(driverUuid)
    }

    const absenceDate = dayjs(command.from).format(DateFormats.DATE)
    if (await this.repository.fullDayAbsenceExists(driverUuid, absenceDate)) {
      throw new AbsenceOverlapsWithFullDayAbsenceError()
    }

    const absence = this.buildAbsence(driverUuid, command)

    if (await this.repository.overlappingAbsenceExists(absence)) {
      throw new AbsenceOverlapsWithAbsenceError()
    }

    const overlappingShifts = await this.repository.findOverlappingShifts(absence)
    const newShifts = this.splitShifts(overlappingShifts, absence)

    const shiftsDeletedEvents = overlappingShifts.map(shift => new ShiftDeletedEvent(shift.uuid))
    const shiftsCreatedEvents = newShifts.map(shift => new ShiftCreatedEvent(shift.uuid))
    const absenceCreatedEvent = new AbsenceCreatedEvent(absence.uuid)

    const events = [
      ...shiftsDeletedEvents,
      ...shiftsCreatedEvents,
      absenceCreatedEvent
    ]

    await transaction(this.dataSource, async () => {
      await this.repository.insertAbsence(absence)
      await this.repository.deleteShifts(overlappingShifts)
      await this.repository.insertShifts(newShifts)
      for (const event of events) {
        await this.eventEmitter.emitOne(event)
      }
    })

    return new CreateAbsenceResponse(absence.uuid)
  }

  private splitShifts (overlappingShifts: Shift[], absence: Absence): Shift[] {
    const shiftSplitter = new ShiftSplitter()
    const absenceDateRange = new DateTimeRange(absence.from, absence.until)
    return overlappingShifts.flatMap(shift => shiftSplitter.split(shift, absenceDateRange))
  }

  private buildAbsence (driverUuid: string, command: CreateAbsenceCommand): Absence {
    return new AbsenceEntityBuilder()
      .withFrom(dayjs(command.from).toDate())
      .withUntil(dayjs(command.until).toDate())
      .withDriverUuid(driverUuid)
      .withType(command.type)
      .build()
  }
}
