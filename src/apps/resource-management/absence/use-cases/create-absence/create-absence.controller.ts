import { Body, Controller, Post } from '@nestjs/common'
import { ApiCreatedResponse, ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { ApiBadRequestErrorResponse, ApiNotFoundErrorResponse } from '../../../../../modules/exceptions/api-errors/api-error-response.decorator.js'
import { AbsenceOverlapsWithAbsenceError } from '../../errors/absence-overlaps-with-absence.error.js'
import { DriverNotFoundError } from '../../../driver/errors/driver-not-found.error.js'
import { AbsenceOverlapsWithFullDayAbsenceError } from '../../errors/absence-overlaps-with-full-day-absence.error.js'
import { Permissions } from '../../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../../modules/permission/permission.enum.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'
import { CreateAbsenceCommand } from './create-absence.command.js'
import { CreateAbsenceResponse } from './create-absence.response.js'
import { CreateAbsenceUseCase } from './create-absence.use-case.js'

@ApiTags('Absence')
@ApiOAuth2([])
@Controller('drivers/:driverUuid/absences')
export class CreateAbsenceController {
  constructor (
    private readonly createAbsenceUseCase: CreateAbsenceUseCase
  ) { }

  @Post()
  @Permissions(Permission.ALL_PERMISSIONS)
  @ApiCreatedResponse({ type: CreateAbsenceResponse, isArray: true })
  @ApiBadRequestErrorResponse(
    AbsenceOverlapsWithAbsenceError,
    AbsenceOverlapsWithFullDayAbsenceError
  )
  @ApiNotFoundErrorResponse(DriverNotFoundError)
  public async createAbsence (
    @UuidParam('driverUuid') driverUuid: DriverUuid,
    @Body() createAbsenceCommand: CreateAbsenceCommand
  ): Promise<CreateAbsenceResponse> {
    return await this.createAbsenceUseCase.execute(driverUuid, createAbsenceCommand)
  }
}
