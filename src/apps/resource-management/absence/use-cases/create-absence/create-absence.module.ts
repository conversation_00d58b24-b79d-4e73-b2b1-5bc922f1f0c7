import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Absence } from '../../entities/absence.entity.js'
import { Shift } from '../../../shift/entities/shift.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { FullDayAbsence } from '../../entities/full-day-absence.entity.js'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { CreateAbsenceUseCase } from './create-absence.use-case.js'
import { CreateAbsenceController } from './create-absence.controller.js'
import { CreateAbsenceRepository } from './create-absence.repository.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      A<PERSON>ence,
      Shift,
      Driver,
      FullDayAbsence
    ]),
    DomainEventEmitterModule
  ],
  controllers: [
    CreateAbsenceController
  ],
  providers: [
    Create<PERSON>bsenceUseCase,
    CreateAbsenceRepository
  ]
})
export class CreateAbsenceModule { }
