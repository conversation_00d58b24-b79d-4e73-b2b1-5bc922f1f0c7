import {
  Column,
  CreateDateColumn, DeleteDateColumn,
  Entity,
  Index, JoinColumn, ManyToOne,
  PrimaryGeneratedColumn, Relation,
  UpdateDateColumn
} from 'typeorm'
import { Driver } from '../../driver/entities/driver/driver.entity.js'
import { AbsenceType } from '../types/absence.type.js'

@Entity()
export class FullDayAbsence {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @DeleteDateColumn({ type: 'timestamptz' })
  deletedAt: Date | null

  @Column({ type: 'date' })
  date: string

  @Column({ type: 'enum', enum: AbsenceType })
  type: AbsenceType

  @Index()
  @Column({ type: 'uuid' })
  driverUuid: string

  @ManyToOne(() => Driver)
  @JoinColumn({ name: 'driver_uuid' })
  driver?: Relation<Driver>
}
