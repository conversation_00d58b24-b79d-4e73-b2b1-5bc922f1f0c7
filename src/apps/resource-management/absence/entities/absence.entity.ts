import {
  Column,
  CreateDateColumn,
  Entity, Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn
} from 'typeorm'
import { Driver } from '../../driver/entities/driver/driver.entity.js'
import { AbsenceType } from '../types/absence.type.js'

@Entity()
@Index(['driverUuid', 'from', 'until'])
export class Absence {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Index()
  @Column({ type: 'uuid' })
  driverUuid: string

  @ManyToOne(() => Driver)
  @JoinColumn({ name: 'driver_uuid' })
  driver?: Relation<Driver>

  @Column({ type: 'timestamptz' })
  from: Date

  @Column({ type: 'timestamptz' })
  until: Date

  @Column({ type: 'enum', enum: AbsenceType })
  type: AbsenceType
}
