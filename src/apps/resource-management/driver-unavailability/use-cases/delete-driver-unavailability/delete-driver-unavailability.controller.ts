import { Controller, Delete } from '@nestjs/common'
import { ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permission } from '../../../../../modules/permission/permission.enum.js'
import { Permissions } from '../../../../../modules/permission/permission.decorator.js'
import { DriverUnavailabilityUuid } from '../../../driver/entities/driver-unavailability/driver-unavailability.uuid.js'
import { DeleteDriverUnavailabilityUseCase } from './delete-driver-unavailability.use-case.js'

@ApiTags('DriverUnavailabilities')
@ApiOAuth2([])
@Controller('drivers/:driverUuid/unavailabilities/:unavailabilityUuid')
export class DeleteDriverUnavailabilityController {
  constructor (
    private readonly deleteDriverUnavailabilityUseCase: DeleteDriverUnavailabilityUseCase
  ) { }

  @Delete()
  @Permissions(Permission.DRIVER_EDIT)
  public async deleteDriverUnavailability (
    @UuidParam('driverUuid') driverUuid: string,
    @UuidParam('unavailabilityUuid') unavailabilityUuid: DriverUnavailabilityUuid
  ): Promise<void> {
    await this.deleteDriverUnavailabilityUseCase.execute(driverUuid, unavailabilityUuid)
  }
}
