import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { DriverUnavailabilityUuid } from '../../../driver/entities/driver-unavailability/driver-unavailability.uuid.js'
import { DriverUnavailability } from '../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'
import { DriverUnavailabilityDeletedEvent } from './driver-unavailability-deleted.event.js'

@Injectable()
export class DeleteDriverUnavailabilityUseCase {
  constructor (
    private readonly dataSource: DataSource,
    @InjectRepository(DriverUnavailability)
    private readonly driverUnavailabilityRepository: Repository<DriverUnavailability>,
    private readonly emitter: DomainEventEmitter
  ) { }

  public async execute (
    driverUuid: string,
    unavailabilityUuid: DriverUnavailabilityUuid
  ): Promise<void> {
    await transaction(this.dataSource, async () => {
      await this.driverUnavailabilityRepository.delete({ uuid: unavailabilityUuid, driverUuid })
      await this.emitter.emitOne(new DriverUnavailabilityDeletedEvent(
        driverUuid,
        unavailabilityUuid
      ))
    })
  }
}
