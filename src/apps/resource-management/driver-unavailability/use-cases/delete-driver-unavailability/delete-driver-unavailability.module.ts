import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { DriverUnavailability } from '../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'
import { DeleteDriverUnavailabilityController } from './delete-driver-unavailability.controller.js'
import { DeleteDriverUnavailabilityUseCase } from './delete-driver-unavailability.use-case.js'

@Module({
  imports: [
    DomainEventEmitterModule,
    TypeOrmModule.forFeature([DriverUnavailability])
  ],
  controllers: [
    DeleteDriverUnavailabilityController
  ],
  providers: [
    DeleteDriverUnavailabilityUseCase
  ]
})
export class DeleteDriverUnavailabilityModule { }
