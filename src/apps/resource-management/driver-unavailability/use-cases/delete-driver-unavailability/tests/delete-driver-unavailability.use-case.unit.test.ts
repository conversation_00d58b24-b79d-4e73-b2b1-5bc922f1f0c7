import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { generateDriverUnavailabilityUuid } from '../../../../driver/entities/driver-unavailability/driver-unavailability.uuid.js'
import { DeleteDriverUnavailabilityUseCase } from '../delete-driver-unavailability.use-case.js'
import { DriverUnavailabilityDeletedEvent } from '../driver-unavailability-deleted.event.js'
import { DriverUnavailability } from '../../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'

describe('DeleteDriverUnavailabilityUseCase Unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('emits an event when deleting a driver unavailability', async () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)

    const driverUuid = randomUUID()
    const driverUnavailabilityUuid = generateDriverUnavailabilityUuid()
    const repository = createStubInstance(Repository<DriverUnavailability>)
    repository.delete.resolves()

    const useCase = new DeleteDriverUnavailabilityUseCase(
      stubDataSource(),
      repository,
      eventEmitter
    )

    await useCase.execute(driverUuid, driverUnavailabilityUuid)

    expect(eventEmitter)
      .toHaveEmitted(new DriverUnavailabilityDeletedEvent(driverUuid, driverUnavailabilityUuid))
  })
})
