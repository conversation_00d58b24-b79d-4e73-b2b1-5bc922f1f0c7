import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'
import { DriverEvent } from '../../../driver/events/driver.event.js'

@OneOfMeta(DomainEventLog, DomainEventType.DRIVER_UNAVAILABILITY_DELETED)
export class DriverUnavailabilityDeletedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly driverUnavailabilityUuid: string

  @ApiProperty({ format: 'uuid' })
  readonly driverUuid: string

  constructor (driverUnavailabilityUuid: string, driverUuid: string) {
    this.driverUnavailabilityUuid = driverUnavailabilityUuid
    this.driverUuid = driverUuid
  }
}

@RegisterDomainEvent(DomainEventType.DRIVER_UNAVAILABILITY_DELETED, 1)
export class DriverUnavailabilityDeletedEvent
  extends DriverEvent<DriverUnavailabilityDeletedEventContent> {
  constructor (driverUnavailabilityUuid: string, driverUuid: string) {
    super({
      driverUuid,
      content: new DriverUnavailabilityDeletedEventContent(driverUnavailabilityUuid, driverUuid)
    })
  }
}
