import { DriverUnavailabilityReason } from '../../../driver/enums/driver-unavailability.enum.js'
import { UpdateDriverUnavailabilityCommand } from './update-driver-unavailability.command.js'

export class UpdateDriverUnavailabilityCommandBuilder {
  private readonly command: UpdateDriverUnavailabilityCommand

  constructor () {
    this.command = new UpdateDriverUnavailabilityCommand()
    this.command.from = new Date().toISOString()
    this.command.until = new Date().toISOString()
    this.command.reason = DriverUnavailabilityReason.ILLNESS
  }

  withFrom (from: string): this {
    this.command.from = from
    return this
  }

  withUntil (until: string): this {
    this.command.until = until
    return this
  }

  withReason (reason: DriverUnavailabilityReason): this {
    this.command.reason = reason
    return this
  }

  build (): UpdateDriverUnavailabilityCommand {
    return this.command
  }
}
