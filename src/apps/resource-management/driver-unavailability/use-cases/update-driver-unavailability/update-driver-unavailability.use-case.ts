import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'

import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { DriverUnavailabilityUuid } from '../../../driver/entities/driver-unavailability/driver-unavailability.uuid.js'
import { DriverUnavailability } from '../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'
import { UpdateDriverUnavailabilityCommand } from './update-driver-unavailability.command.js'
import { DriverUnavailabilityUpdatedEvent } from './driver-unavailability-updated.event.js'

@Injectable()
export class UpdateDriverUnavailabilityUseCase {
  constructor (
    private readonly dataSource: DataSource,
    @InjectRepository(DriverUnavailability)
    private readonly driverUnavailabilitiesRepository: Repository<DriverUnavailability>,
    private readonly emitter: DomainEventEmitter
  ) { }

  public async execute (
    driverUuid: string,
    unavailabilityUuid: DriverUnavailabilityUuid,
    command: UpdateDriverUnavailabilityCommand
  ): Promise<void> {
    await this.driverUnavailabilitiesRepository.findOneByOrFail({
      uuid: unavailabilityUuid,
      driverUuid
    })

    await transaction(this.dataSource, async () => {
      await this.driverUnavailabilitiesRepository.update({ uuid: unavailabilityUuid, driverUuid }, {
        from: command.from,
        until: command.until,
        reason: command.reason
      })
      await this.emitter.emitOne(new DriverUnavailabilityUpdatedEvent(
        driverUuid,
        unavailabilityUuid
      ))
    })
  }
}
