import { before, describe, it, after } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import type { DataSource } from 'typeorm'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestAuthContext } from '../../../../../../../test/utils/test-auth-context.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { BranchEntityBuilder } from '../../../../branch/branch.entity-builder.js'
import { Branch } from '../../../../branch/branch.entity.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { UpdateDriverUnavailabilityCommandBuilder } from '../update-driver-unavailability.command.builder.js'
import { DriverUnavailabilityReason } from '../../../../driver/enums/driver-unavailability.enum.js'
import { Driver } from '../../../../driver/entities/driver/driver.entity.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { DriverEntityBuilder } from '../../../../driver/entities/driver/driver.entity.builder.js'
import { DriverUnavailability } from '../../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'
import { DriverUnavailabilityEntityBuilder } from '../../../../driver/entities/driver-unavailability/driver-unavailability.entity-builder.js'

describe('Update driver unavailability end to end tests', () => {
  let setup: EndToEndTestSetup
  let dataSource: DataSource
  let context: TestAuthContext
  let adminUser: TestUser
  let defaultUser: TestUser

  let driver: Driver
  let driverUnavailability: DriverUnavailability

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    context = setup.authContext
    dataSource = setup.dataSource

    adminUser = await context.getAdminUser()
    defaultUser = await context.getDefaultUser()

    const location = new LocationBuilder().build()

    const shift = new DriverDefaultShiftEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .build()

    const branch = new BranchEntityBuilder().build()

    await setup.dataSource.manager.insert(Location, location)
    await setup.dataSource.manager.insert(DriverDefaultShift, shift)
    await setup.dataSource.manager.insert(Branch, branch)

    driver = new DriverEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withDefaultShiftUuid(shift.uuid)
      .build()

    await setup.dataSource.manager.save(Driver, driver)

    driverUnavailability = new DriverUnavailabilityEntityBuilder()
      .withDriverUuid(driver.uuid)
      .build()

    await dataSource.manager.save(DriverUnavailability, driverUnavailability)
  })

  after(async () => await setup.teardown())

  it('should return 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .put(`/drivers/${driver.uuid}/unavailabilities/${driverUnavailability.uuid}`)

    expect(response).toHaveStatus(401)
  })

  it('should return 403 when not authorized', async () => {
    const response = await request(setup.httpServer)
      .put(`/drivers/${driver.uuid}/unavailabilities/${driverUnavailability.uuid}`)
      .set('Authorization', `Bearer ${defaultUser.token}`)

    expect(response).toHaveStatus(403)
  })

  it('should update driver unavailability', async () => {
    const command = new UpdateDriverUnavailabilityCommandBuilder()
      .withReason(DriverUnavailabilityReason.ILLNESS)
      .build()

    const response = await request(setup.httpServer)
      .put(`/drivers/${driver.uuid}/unavailabilities/${driverUnavailability.uuid}`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command)

    expect(response).toHaveStatus(200)
  })
})
