import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { generateDriverUnavailabilityUuid } from '../../../../driver/entities/driver-unavailability/driver-unavailability.uuid.js'
import { UpdateDriverUnavailabilityUseCase } from '../update-driver-unavailability.use-case.js'
import { UpdateDriverUnavailabilityCommandBuilder } from '../update-driver-unavailability.command.builder.js'
import { DriverUnavailabilityUpdatedEvent } from '../driver-unavailability-updated.event.js'
import { DriverUnavailability } from '../../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'

describe('UpdateDriverUnavailabilityUseCase Unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('emits an event when updating a driver unavailability', async () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)

    const driverUuid = randomUUID()
    const driverUnavailabilityUuid = generateDriverUnavailabilityUuid()
    const repository = createStubInstance(Repository<DriverUnavailability>)
    repository.update.resolves()

    const useCase = new UpdateDriverUnavailabilityUseCase(
      stubDataSource(),
      repository,
      eventEmitter
    )

    const command = new UpdateDriverUnavailabilityCommandBuilder().build()

    await useCase.execute(driverUuid, driverUnavailabilityUuid, command)

    expect(eventEmitter)
      .toHaveEmitted(new DriverUnavailabilityUpdatedEvent(driverUuid, driverUnavailabilityUuid))
  })
})
