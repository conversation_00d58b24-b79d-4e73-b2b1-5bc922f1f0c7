import { ApiProperty } from '@nestjs/swagger'
import { IsDateString, IsEnum } from 'class-validator'
import { DriverUnavailabilityReason, DriverUnavailabilityReasonApiProperty } from '../../../driver/enums/driver-unavailability.enum.js'

export class UpdateDriverUnavailabilityCommand {
  @ApiProperty({ type: String, format: 'date' })
  @IsDateString()
  from: string

  @ApiProperty({ type: String, format: 'date' })
  @IsDateString()
  until: string

  @DriverUnavailabilityReasonApiProperty()
  @IsEnum(DriverUnavailabilityReason)
  reason: DriverUnavailabilityReason
}
