import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { DriverUnavailability } from '../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'
import { UpdateDriverUnavailabilityController } from './update-driver-unavailability.controller.js'
import { UpdateDriverUnavailabilityUseCase } from './update-driver-unavailability.use-case.js'

@Module({
  imports: [
    DomainEventEmitterModule,
    TypeOrmModule.forFeature([DriverUnavailability])
  ],
  controllers: [
    UpdateDriverUnavailabilityController
  ],
  providers: [
    UpdateDriverUnavailabilityUseCase
  ]
})
export class UpdateDriverUnavailabilityModule { }
