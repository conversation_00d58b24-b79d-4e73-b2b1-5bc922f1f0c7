import { Body, Controller, Put } from '@nestjs/common'
import { ApiTags, ApiOAuth2 } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permission } from '../../../../../modules/permission/permission.enum.js'
import { Permissions } from '../../../../../modules/permission/permission.decorator.js'
import { DriverUnavailabilityUuid } from '../../../driver/entities/driver-unavailability/driver-unavailability.uuid.js'
import { UpdateDriverUnavailabilityCommand } from './update-driver-unavailability.command.js'
import { UpdateDriverUnavailabilityUseCase } from './update-driver-unavailability.use-case.js'

@ApiTags('DriverUnavailabilities')
@ApiOAuth2([])
@Controller('drivers/:driverUuid/unavailabilities/:unavailabilityUuid')
export class UpdateDriverUnavailabilityController {
  constructor (
    private readonly updateDriverUnavailabilityUseCase: UpdateDriverUnavailabilityUseCase
  ) { }

  @Put()
  @Permissions(Permission.DRIVER_EDIT)
  public async updateDriverUnavailability (
    @UuidParam('driverUuid') driverUuid: string,
    @UuidParam('unavailabilityUuid') unavailabilityUuid: DriverUnavailabilityUuid,
    @Body() command: UpdateDriverUnavailabilityCommand
  ): Promise<void> {
    await this.updateDriverUnavailabilityUseCase.execute(driverUuid, unavailabilityUuid, command)
  }
}
