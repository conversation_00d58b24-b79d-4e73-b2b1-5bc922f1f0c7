import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'

import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'
import { DriverEvent } from '../../../driver/events/driver.event.js'

@OneOfMeta(DomainEventLog, DomainEventType.DRIVER_UNAVAILABILITY_UPDATED)
export class DriverUnavailabilityUpdatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly driverUnavailabilityUuid: string

  @ApiProperty({ format: 'uuid' })
  readonly driverUuid: string

  constructor (driverUnavailabilitiesUuid: string, driverUuid: string) {
    this.driverUnavailabilityUuid = driverUnavailabilitiesUuid
    this.driverUuid = driverUuid
  }
}

@RegisterDomainEvent(DomainEventType.DRIVER_UNAVAILABILITY_UPDATED, 1)
export class DriverUnavailabilityUpdatedEvent
  extends DriverEvent<DriverUnavailabilityUpdatedEventContent> {
  constructor (driverUuid: string, driverUnavailabilityUuid: string) {
    super({
      driverUuid,
      content: new DriverUnavailabilityUpdatedEventContent(driverUnavailabilityUuid, driverUuid)
    })
  }
}
