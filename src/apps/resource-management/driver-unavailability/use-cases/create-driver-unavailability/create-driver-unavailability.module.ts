import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { DriverUnavailability } from '../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { CreateDriverUnavailabilityController } from './create-driver-unavailability.controller.js'
import { CreateDriverUnavailabilityUseCase } from './create-driver-unavailability.use-case.js'

@Module({
  imports: [
    DomainEventEmitterModule,
    TypeOrmModule.forFeature([Driver, DriverUnavailability])
  ],
  controllers: [
    CreateDriverUnavailabilityController
  ],
  providers: [
    CreateDriverUnavailabilityUseCase
  ]
})
export class CreateDriverUnavailabilityModule { }
