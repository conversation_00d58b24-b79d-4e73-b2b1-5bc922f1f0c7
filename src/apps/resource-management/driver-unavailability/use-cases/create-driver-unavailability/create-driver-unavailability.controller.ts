import { Body, Controller, Post } from '@nestjs/common'
import { ApiCreatedResponse, ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permission } from '../../../../../modules/permission/permission.enum.js'
import { Permissions } from '../../../../../modules/permission/permission.decorator.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'
import { CreateDriverUnavailabilityUseCase } from './create-driver-unavailability.use-case.js'
import { CreateDriverUnavailabilityResponse } from './create-driver-unavailability.response.js'
import { CreateDriverUnavailabilityCommand } from './create-driver-unavailability.command.js'

@ApiTags('DriverUnavailabilities')
@ApiOAuth2([])
@Controller('drivers/:driverUuid/unavailabilities')
export class CreateDriverUnavailabilityController {
  constructor (
    private readonly createDriverUnavailabilityUseCase: CreateDriverUnavailabilityUseCase
  ) { }

  @Post()
  @Permissions(Permission.DRIVER_EDIT)
  @ApiCreatedResponse({ type: CreateDriverUnavailabilityResponse })
  public async createDriverUnavailabity (
    @UuidParam('driverUuid') driverUuid: DriverUuid,
    @Body() createDriverUnavailabilityCommand: CreateDriverUnavailabilityCommand
  ): Promise<CreateDriverUnavailabilityResponse> {
    return this.createDriverUnavailabilityUseCase.execute(
      driverUuid,
      createDriverUnavailabilityCommand
    )
  }
}
