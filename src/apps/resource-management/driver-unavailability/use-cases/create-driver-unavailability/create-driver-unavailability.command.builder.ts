import { DriverUnavailabilityReason } from '../../../driver/enums/driver-unavailability.enum.js'
import { CreateDriverUnavailabilityCommand } from './create-driver-unavailability.command.js'

export class CreateDriverUnavailabilityCommandBuilder {
  private readonly command: CreateDriverUnavailabilityCommand

  constructor () {
    this.command = new CreateDriverUnavailabilityCommand()
    this.command.from = new Date().toISOString()
    this.command.until = new Date().toISOString()
    this.command.reason = DriverUnavailabilityReason.ILLNESS
  }

  withFrom (from: string): this {
    this.command.from = from
    return this
  }

  withUntil (until: string): this {
    this.command.until = until
    return this
  }

  withReason (reason: DriverUnavailabilityReason): this {
    this.command.reason = reason
    return this
  }

  build (): CreateDriverUnavailabilityCommand {
    return this.command
  }
}
