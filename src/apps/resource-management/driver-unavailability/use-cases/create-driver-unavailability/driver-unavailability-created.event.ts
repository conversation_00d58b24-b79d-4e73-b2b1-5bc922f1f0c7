import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'
import { DriverEvent } from '../../../driver/events/driver.event.js'

@OneOfMeta(DomainEventLog, DomainEventType.DRIVER_UNAVAILABILITY_CREATED)
export class DriverUnavailabilityCreatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly driverUuid: string

  @ApiProperty({ format: 'uuid' })
  readonly driverUnavailabilityUuid: string

  constructor (driverUuid: string, driverUnavailabilityUuid: string) {
    this.driverUuid = driverUuid
    this.driverUnavailabilityUuid = driverUnavailabilityUuid
  }
}

@RegisterDomainEvent(DomainEventType.DRIVER_UNAVAILABILITY_CREATED, 1)
export class DriverUnavailabilityCreatedEvent
  extends DriverEvent<DriverUnavailabilityCreatedEventContent> {
  constructor (driverUuid: string, driverUnavailabilityUuid: string) {
    super({
      driverUuid,
      content: new DriverUnavailabilityCreatedEventContent(driverUuid, driverUnavailabilityUuid)
    })
  }
}
