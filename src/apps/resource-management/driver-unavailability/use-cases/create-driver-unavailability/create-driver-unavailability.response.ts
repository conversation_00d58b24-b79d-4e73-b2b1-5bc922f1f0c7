import { ApiProperty } from '@nestjs/swagger'
import { DriverUnavailability } from '../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'

export class CreateDriverUnavailabilityResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'uuid' })
  driverUuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  from: string

  @ApiProperty({ type: String, format: 'date-time' })
  until: string

  @ApiProperty({ type: String })
  reason: string

  constructor (driverUnavailability: DriverUnavailability) {
    this.uuid = driverUnavailability.uuid
    this.driverUuid = driverUnavailability.driverUuid
    this.from = driverUnavailability.from.toISOString()
    this.until = driverUnavailability.until.toISOString()
    this.reason = driverUnavailability.reason
  }
}
