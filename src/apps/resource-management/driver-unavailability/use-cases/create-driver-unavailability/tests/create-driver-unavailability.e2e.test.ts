import { before, describe, it, after } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TestAuthContext } from '../../../../../../../test/utils/test-auth-context.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'

import { DriverEntityBuilder } from '../../../../driver/entities/driver/driver.entity.builder.js'
import { Driver } from '../../../../driver/entities/driver/driver.entity.js'
import { CreateDriverUnavailabilityCommandBuilder } from '../create-driver-unavailability.command.builder.js'
import { CreateVehicleUnavailabilityCommandBuilder } from '../../../../vehicle/use-cases/create-vehicle-unavailability/create-vehicle-unavailability.command.builder.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { BranchEntityBuilder } from '../../../../branch/branch.entity-builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { Branch } from '../../../../branch/branch.entity.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../driver-default-shift/entities/driver-default-shift.entity.js'

describe('Create driver unavailability end to end tests', () => {
  let setup: EndToEndTestSetup
  let context: TestAuthContext
  let adminUser: TestUser
  let defaultUser: TestUser

  let driver: Driver

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    context = setup.authContext

    adminUser = await context.getAdminUser()
    defaultUser = await context.getDefaultUser()

    const location = new LocationBuilder().build()

    const shift = new DriverDefaultShiftEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .build()

    const branch = new BranchEntityBuilder().build()

    await setup.dataSource.manager.insert(Location, location)
    await setup.dataSource.manager.insert(DriverDefaultShift, shift)
    await setup.dataSource.manager.insert(Branch, branch)

    driver = new DriverEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withDefaultShiftUuid(shift.uuid)
      .build()

    await setup.dataSource.manager.save(Driver, driver)
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const command = new CreateDriverUnavailabilityCommandBuilder()
      .build()

    const response = await request(setup.httpServer)
      .post(`/drivers/${driver.uuid}/unavailabilities`)
      .send(command)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when not authorized', async () => {
    const command = new CreateVehicleUnavailabilityCommandBuilder().build()

    const response = await request(setup.httpServer)
      .post(`/drivers/${driver.uuid}/unavailabilities`)
      .set('Authorization', `Bearer ${defaultUser.token}`)
      .send(command)

    expect(response).toHaveStatus(403)
  })

  it('creates driver unavailability', async () => {
    const command = new CreateDriverUnavailabilityCommandBuilder()
      .build()

    const response = await request(setup.httpServer)
      .post(`/drivers/${driver.uuid}/unavailabilities`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command)

    expect(response).toHaveStatus(201)
  })
})
