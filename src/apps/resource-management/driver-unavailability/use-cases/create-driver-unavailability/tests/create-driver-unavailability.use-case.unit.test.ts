import { before, describe, it } from 'node:test'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { InsertResult, Repository } from 'typeorm'
import { Driver } from 'typeorm/browser'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { generateDriverUnavailabilityUuid } from '../../../../driver/entities/driver-unavailability/driver-unavailability.uuid.js'
import { CreateDriverUnavailabilityUseCase } from '../create-driver-unavailability.use-case.js'
import { CreateDriverUnavailabilityCommandBuilder } from '../create-driver-unavailability.command.builder.js'
import { DriverUnavailabilityCreatedEvent } from '../driver-unavailability-created.event.js'
import { DriverUnavailability } from '../../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'
import { generateDriverUuid } from '../../../../driver/entities/driver/driver.uuid.js'

describe('CreateDriverUnavailabilityUseCase Unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('emits an event when creating a driver unavailability', async () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)

    const driverUuid = generateDriverUuid()
    const driverUnavailabilityUuid = generateDriverUnavailabilityUuid()
    const driverUnavailabilityRepository = createStubInstance(Repository<DriverUnavailability>)
    const driverRepository = createStubInstance(Repository<Driver>)

    driverRepository.findOneByOrFail.resolves({ uuid: driverUuid })

    driverUnavailabilityRepository.create.returns({})
    driverUnavailabilityRepository.insert
      .callsFake((driverUnavailability: DriverUnavailability) => {
        driverUnavailability.uuid = driverUnavailabilityUuid
        driverUnavailability.driverUuid = driverUuid
        return Promise.resolve({} as InsertResult)
      })

    const useCase = new CreateDriverUnavailabilityUseCase
    (
      stubDataSource(),
      driverRepository,
      driverUnavailabilityRepository,
      eventEmitter
    )

    const command = new CreateDriverUnavailabilityCommandBuilder()
      .build()

    await useCase.execute(driverUuid, command)

    expect(eventEmitter)
      .toHaveEmitted(new DriverUnavailabilityCreatedEvent(driverUuid, driverUnavailabilityUuid))
  })
})
