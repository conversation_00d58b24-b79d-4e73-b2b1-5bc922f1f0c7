import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { Injectable } from '@nestjs/common'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { DriverUnavailabilityEntityBuilder } from '../../../driver/entities/driver-unavailability/driver-unavailability.entity-builder.js'
import { DriverUnavailability } from '../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'
import { CreateDriverUnavailabilityCommand } from './create-driver-unavailability.command.js'
import { CreateDriverUnavailabilityResponse } from './create-driver-unavailability.response.js'
import { DriverUnavailabilityCreatedEvent } from './driver-unavailability-created.event.js'

@Injectable()
export class CreateDriverUnavailabilityUseCase {
  constructor (
    private readonly dataSource: DataSource,
    @InjectRepository(Driver)
    private readonly driverRepository: Repository<Driver>,
    @InjectRepository(DriverUnavailability)
    private readonly driverUnavailabilityRepository: Repository<DriverUnavailability>,
    private readonly emitter: DomainEventEmitter
  ) { }

  public async execute (
    driverUuid: DriverUuid,
    command: CreateDriverUnavailabilityCommand
  ): Promise<CreateDriverUnavailabilityResponse> {
    await this.driverRepository.findOneByOrFail({ uuid: driverUuid })

    const driverUnavailability = new DriverUnavailabilityEntityBuilder()
      .withDriverUuid(driverUuid)
      .withFrom(new Date(command.from))
      .withUntil(new Date(command.until))
      .withReason(command.reason)
      .build()

    await transaction(this.dataSource, async () => {
      await this.driverUnavailabilityRepository.insert(driverUnavailability)
      await this.emitter.emitOne(new DriverUnavailabilityCreatedEvent(
        driverUnavailability.driverUuid,
        driverUnavailability.uuid
      ))
    })

    return new CreateDriverUnavailabilityResponse(driverUnavailability)
  }
}
