import { Module } from '@nestjs/common'
import { DeleteDriverUnavailabilityModule } from './use-cases/delete-driver-unavailability/delete-driver-unavailability.module.js'
import { UpdateDriverUnavailabilityModule } from './use-cases/update-driver-unavailability/update-driver-unavailability.module.js'
import { CreateDriverUnavailabilityModule } from './use-cases/create-driver-unavailability/create-driver-unavailability.module.js'

@Module({
  imports: [
    CreateDriverUnavailabilityModule,
    UpdateDriverUnavailabilityModule,
    DeleteDriverUnavailabilityModule
  ]
})
export class DriverUnavailabilityModule { }
