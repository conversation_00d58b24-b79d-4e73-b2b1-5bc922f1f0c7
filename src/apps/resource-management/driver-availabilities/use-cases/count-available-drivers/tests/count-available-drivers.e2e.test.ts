import { after, before, describe, it } from 'node:test'
import supertest from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { stringify } from 'qs'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { BranchEntityBuilder } from '../../../../branch/builders/branch.entity.builder.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import {
  DriverDefaultShiftEntityBuilder
} from '../../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverEntityBuilder } from '../../../../driver/entities/driver/driver.entity.builder.js'
import { Branch } from '../../../../branch/branch.entity.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import {
  DriverDefaultShift
} from '../../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { Driver } from '../../../../driver/entities/driver/driver.entity.js'
import { ShiftEntityBuilder } from '../../../../shift/builders/shift.entity.builder.js'
import { Shift } from '../../../../shift/entities/shift.entity.js'
import { CountAvailableDriversQuery } from '../count-available-drivers.query.js'

describe('Get driver availabilities e2e test', () => {
  let setup: EndToEndTestSetup
  let adminUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    adminUser = await setup.authContext.getAdminUser()
  })

  after(async () => await setup.teardown())

  it('responds with the amount of available drivers', async () => {
    const branch = new BranchEntityBuilder().build()
    const location = new LocationBuilder().build()
    const defaultShift = new DriverDefaultShiftEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .build()
    const defaultShift2 = new DriverDefaultShiftEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .build()
    const defaultShift3 = new DriverDefaultShiftEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .build()
    const driver = new DriverEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withDefaultShiftUuid(defaultShift.uuid)
      .build()
    const hiddenDriver = new DriverEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withDefaultShiftUuid(defaultShift2.uuid)
      .build()
    const unavailableDriver = new DriverEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withDefaultShiftUuid(defaultShift3.uuid)
      .build()

    const driverShift1 = new ShiftEntityBuilder()
      .withDriverUuid(driver.uuid)
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .withBranchUuid(branch.uuid)
      .withFrom(new Date('2024-01-01 10:00:00'))
      .withUntil(new Date('2024-01-01 12:00:00'))
      .build()

    const driverShift2 = new ShiftEntityBuilder()
      .copyShift(driverShift1)
      .withFrom(new Date('2024-01-01 14:00:00'))
      .withUntil(new Date('2024-01-01 19:00:00'))
      .build()

    const hiddenDriverShift = new ShiftEntityBuilder()
      .copyShift(driverShift1)
      .withDriverUuid(hiddenDriver.uuid)
      .withIsHidden(true)
      .withFrom(new Date('2024-01-01 10:00:00'))
      .withUntil(new Date('2024-01-01 19:00:00'))
      .build()

    const unavailableDriverShift = new ShiftEntityBuilder()
      .copyShift(driverShift1)
      .withDriverUuid(unavailableDriver.uuid)
      .withFrom(new Date('2024-01-02 10:00:00'))
      .withUntil(new Date('2024-01-02 19:00:00'))
      .build()

    await setup.dataSource.manager.insert(Branch, branch)
    await setup.dataSource.manager.insert(Location, location)
    await setup.dataSource.manager.insert(DriverDefaultShift, [
      defaultShift, defaultShift2, defaultShift3
    ])
    await setup.dataSource.manager.insert(Driver, [driver, hiddenDriver, unavailableDriver])
    await setup.dataSource.manager.insert(Shift, [
      driverShift1, driverShift2, unavailableDriverShift, hiddenDriverShift
    ])

    const query = new CountAvailableDriversQuery()
    query.date = '2024-01-01'

    const response = await supertest(setup.httpServer)
      .get(`/count-available-drivers`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(HttpStatus.OK)
    expect(response.body).toStrictEqual({
      count: 1
    })
  })
})
