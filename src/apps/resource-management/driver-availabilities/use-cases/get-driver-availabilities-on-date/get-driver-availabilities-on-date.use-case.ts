import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { Shift } from '../../../shift/entities/shift.entity.js'
import { Absence } from '../../../absence/entities/absence.entity.js'
import { OnDate } from '../../../../../utils/typeorm/operators/timestamp-lies-on-date.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { FullDayAbsence } from '../../../absence/entities/full-day-absence.entity.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'
import {
  GetDriverAvailabilitiesOnDateResponse
} from './get-driver-availabilities-on-date.response.js'

@Injectable()
export class GetDriverAvailabilitiesOnDateUseCase {
  constructor (
    @InjectRepository(Driver) private readonly driverRepository: Repository<Driver>,
    @InjectRepository(Shift) private readonly shiftRepository: Repository<Shift>,
    @InjectRepository(Absence) private readonly absenceRepository: Repository<Absence>,
    @InjectRepository(FullDayAbsence)
    private readonly fullDayAbsenceRepository: Repository<FullDayAbsence>
  ) {}

  async getAvailabilities (
    forDriverUuid: DriverUuid,
    onDate: string
  ): Promise<GetDriverAvailabilitiesOnDateResponse> {
    const [driver, shifts, absences, fullDayAbsence] = await Promise.all([
      this.fetchDriver(forDriverUuid),
      this.fetchShifts(forDriverUuid, onDate),
      this.fetchAbsences(forDriverUuid, onDate),
      this.fetchFullDayAbsence(forDriverUuid, onDate)
    ])

    return new GetDriverAvailabilitiesOnDateResponse(driver, shifts, absences, fullDayAbsence)
  }

  private fetchShifts (forDriverUuid: string, onDate: string): Promise<Shift[]> {
    return this.shiftRepository.find({
      where: {
        driverUuid: forDriverUuid,
        from: OnDate(onDate)
      },
      relations: {
        startLocation: true,
        stopLocation: true
      }
    })
  }

  private fetchAbsences (forDriverUuid: string, onDate: string): Promise<Absence[]> {
    return this.absenceRepository.find({
      where: {
        driverUuid: forDriverUuid,
        from: OnDate(onDate)
      }
    })
  }

  private fetchDriver (forDriverUuid: DriverUuid): Promise<Driver> {
    return this.driverRepository.findOneOrFail({
      select: { uuid: true, firstName: true, lastName: true },
      where: { uuid: forDriverUuid }
    })
  }

  private async fetchFullDayAbsence (
    forDriverUuid: string,
    onDate: string
  ): Promise<FullDayAbsence | null> {
    return await this.fullDayAbsenceRepository.findOneBy({
      driverUuid: forDriverUuid,
      date: onDate
    })
  }
}
