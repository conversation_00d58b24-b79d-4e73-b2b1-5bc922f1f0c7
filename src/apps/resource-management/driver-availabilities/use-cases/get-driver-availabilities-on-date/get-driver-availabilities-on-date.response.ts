import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { LocationResponse } from '../../../../../modules/location/location.response.js'
import { Shift } from '../../../shift/entities/shift.entity.js'
import { AbsenceType, AbsenceTypeApiProperty } from '../../../absence/types/absence.type.js'
import { Absence } from '../../../absence/entities/absence.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { FullDayAbsence } from '../../../absence/entities/full-day-absence.entity.js'

class GetDriverAvailabilitiesOnDateDriverResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  firstName: string

  @ApiProperty({ type: String })
  lastName: string

  constructor (driver: Driver) {
    this.uuid = driver.uuid
    this.firstName = driver.firstName
    this.lastName = driver.lastName
  }
}

class GetDriverAvailabilitiesOnDateShiftResponse {
  @ApiProperty({ type: 'string', format: 'uuid' })
  uuid: string

  @ApiProperty({ type: 'string', format: 'date-time' })
  from: string

  @ApiProperty({ type: 'string', format: 'date-time' })
  until: string

  @ApiProperty({ type: 'string' })
  licensePlate: string

  @ApiProperty({ type: LocationResponse })
  startLocation: LocationResponse

  @ApiProperty({ type: LocationResponse })
  stopLocation: LocationResponse

  constructor (shift: Shift) {
    this.uuid = shift.uuid
    this.from = shift.from.toISOString()
    this.until = shift.until.toISOString()
    this.licensePlate = shift.licensePlate

    assert(shift.startLocation !== undefined, 'Start location not loaded')
    this.startLocation = new LocationResponse(shift.startLocation)

    assert(shift.stopLocation !== undefined, 'Stop location not loaded')
    this.stopLocation = new LocationResponse(shift.stopLocation)
  }
}

class GetDriverAvailabilitiesOnDateFullDayAbsenceResponse {
  @ApiProperty({ type: 'string', format: 'uuid' })
  uuid: string

  @AbsenceTypeApiProperty()
  type: AbsenceType

  constructor (absence: FullDayAbsence) {
    this.uuid = absence.uuid
    this.type = absence.type
  }
}

class GetDriverAvailabilitiesOnDateAbsenceResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  from: string

  @ApiProperty({ type: String, format: 'date-time' })
  until: string

  @AbsenceTypeApiProperty()
  type: AbsenceType

  constructor (absence: Absence) {
    this.uuid = absence.uuid
    this.from = absence.from.toISOString()
    this.until = absence.until.toISOString()
    this.type = absence.type
  }
}

export class GetDriverAvailabilitiesOnDateResponse {
  @ApiProperty({ type: GetDriverAvailabilitiesOnDateDriverResponse })
  driver: GetDriverAvailabilitiesOnDateDriverResponse

  @ApiProperty({ type: GetDriverAvailabilitiesOnDateFullDayAbsenceResponse, nullable: true })
  fullDayAbsence: GetDriverAvailabilitiesOnDateFullDayAbsenceResponse | null

  @ApiProperty({ type: GetDriverAvailabilitiesOnDateShiftResponse, isArray: true })
  shifts: GetDriverAvailabilitiesOnDateShiftResponse[]

  @ApiProperty({ type: GetDriverAvailabilitiesOnDateAbsenceResponse, isArray: true })
  absences: GetDriverAvailabilitiesOnDateAbsenceResponse[]

  constructor (
    driver: Driver,
    shifts: Shift[],
    absences: Absence[],
    fullDayAbsence: FullDayAbsence | null
  ) {
    this.driver = new GetDriverAvailabilitiesOnDateDriverResponse(driver)
    this.fullDayAbsence = (fullDayAbsence !== null)
      ? new GetDriverAvailabilitiesOnDateFullDayAbsenceResponse(fullDayAbsence)
      : null
    this.shifts = shifts.map(
      shift => new GetDriverAvailabilitiesOnDateShiftResponse(shift))
    this.absences = absences.map(
      absence => new GetDriverAvailabilitiesOnDateAbsenceResponse(absence))
  }
}
