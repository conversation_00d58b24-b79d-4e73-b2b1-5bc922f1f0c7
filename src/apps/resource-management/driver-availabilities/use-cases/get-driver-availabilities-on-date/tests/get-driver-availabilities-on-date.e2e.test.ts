import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { stringify } from 'qs'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'

import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { BranchEntityBuilder } from '../../../../branch/builders/branch.entity.builder.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { DriverEntityBuilder } from '../../../../driver/entities/driver/driver.entity.builder.js'
import { ShiftEntityBuilder } from '../../../../shift/builders/shift.entity.builder.js'
import { AbsenceEntityBuilder } from '../../../../absence/builders/absence.entity.builder.js'
import { AbsenceType } from '../../../../absence/types/absence.type.js'
import { Branch } from '../../../../branch/branch.entity.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { Driver } from '../../../../driver/entities/driver/driver.entity.js'
import { Shift } from '../../../../shift/entities/shift.entity.js'
import { Absence } from '../../../../absence/entities/absence.entity.js'
import {
  GetDriverAvailabilitiesOnDateQueryBuilder
} from '../get-driver-availabilities-on-date.query.builder.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../driver-default-shift/entities/driver-default-shift.entity.js'

describe('Get driver availabilities on date e2e test', () => {
  let setup: EndToEndTestSetup
  let adminUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    adminUser = await setup.authContext.getAdminUser()
  })

  after(async () => await setup.teardown())

  it('returns driver availabilities on date', async () => {
    const branch = new BranchEntityBuilder().build()
    const location = new LocationBuilder().build()
    const defaultShift = new DriverDefaultShiftEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .build()
    const driver = new DriverEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withDefaultShift(defaultShift)
      .build()

    const shifts = [
      new ShiftEntityBuilder()
        .withFrom(new Date('2024-01-01 10:00:00'))
        .withUntil(new Date('2024-01-01 12:00:00'))
        .withStopLocationUuid(location.uuid)
        .withStartLocationUuid(location.uuid)
        .withDriverUuid(driver.uuid)
        .withBranchUuid(branch.uuid)
        .build(),
      new ShiftEntityBuilder()
        .withFrom(new Date('2024-01-01 16:00:00'))
        .withUntil(new Date('2024-01-01 18:00:00'))
        .withStopLocationUuid(location.uuid)
        .withStartLocationUuid(location.uuid)
        .withDriverUuid(driver.uuid)
        .withBranchUuid(branch.uuid)
        .build(),
      new ShiftEntityBuilder()
        .withFrom(new Date('2024-01-02 00:00:00'))
        .withUntil(new Date('2024-01-02 18:00:00'))
        .withStopLocationUuid(location.uuid)
        .withStartLocationUuid(location.uuid)
        .withDriverUuid(driver.uuid)
        .withBranchUuid(branch.uuid)
        .build()
    ]

    const absences = [
      new AbsenceEntityBuilder()
        .withDriverUuid(driver.uuid)
        .withFrom(new Date('2024-01-01 12:00:00'))
        .withUntil(new Date('2024-01-01 16:00:00'))
        .withType(AbsenceType.OTHER)
        .build(),
      new AbsenceEntityBuilder()
        .withDriverUuid(driver.uuid)
        .withFrom(new Date('2024-01-02 19:00:00'))
        .withUntil(new Date('2024-01-02 20:00:00'))
        .withType(AbsenceType.OTHER)
        .build()
    ]

    await setup.dataSource.manager.insert(Branch, branch)
    await setup.dataSource.manager.insert(Location, location)
    await setup.dataSource.manager.insert(DriverDefaultShift, defaultShift)
    await setup.dataSource.manager.insert(Driver, driver)
    await setup.dataSource.manager.insert(Shift, shifts)
    await setup.dataSource.manager.insert(Absence, absences)

    const query = new GetDriverAvailabilitiesOnDateQueryBuilder()
      .withDate('2024-01-01')
      .build()

    const response = await request(setup.httpServer)
      .get(`/driver-availabilities/${driver.uuid}`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(HttpStatus.OK)
    expect(response.body).toStrictEqual({
      driver: {
        uuid: driver.uuid,
        firstName: driver.firstName,
        lastName: driver.lastName
      },
      fullDayAbsence: null,
      shifts: expect.arrayContaining([
        expect.objectContaining({ uuid: shifts[0].uuid }),
        expect.objectContaining({ uuid: shifts[1].uuid })
      ]),
      absences: expect.arrayContaining([
        expect.objectContaining({ uuid: absences[0].uuid })
      ])
    })
  })
})
