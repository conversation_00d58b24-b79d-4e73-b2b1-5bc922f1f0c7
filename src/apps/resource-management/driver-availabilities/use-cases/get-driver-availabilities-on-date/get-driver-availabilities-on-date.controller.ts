import { Controller, Get, Query } from '@nestjs/common'
import { ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'
import {
  GetDriverAvailabilitiesOnDateUseCase
} from './get-driver-availabilities-on-date.use-case.js'
import { GetDriverAvailabilitiesOnDateQuery } from './get-driver-availabilities-on-date.query.js'
import {
  GetDriverAvailabilitiesOnDateResponse
} from './get-driver-availabilities-on-date.response.js'

@Controller('/driver-availabilities/:driverUuid')
@ApiTags('Driver Availability')
export class GetDriverAvailabilitiesOnDateController {
  constructor (
    private readonly useCase: GetDriverAvailabilitiesOnDateUseCase
  ) {}

  @Get()
  @ApiOkResponse({ type: GetDriverAvailabilitiesOnDateResponse })
  async getAvailabilities (
    @UuidParam('driverUuid') driverUuid: DriverUuid,
    @Query() query: GetDriverAvailabilitiesOnDateQuery
  ): Promise<GetDriverAvailabilitiesOnDateResponse> {
    return await this.useCase.getAvailabilities(driverUuid, query.date)
  }
}
