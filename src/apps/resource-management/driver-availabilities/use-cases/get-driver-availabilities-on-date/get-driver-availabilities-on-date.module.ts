import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Shift } from '../../../shift/entities/shift.entity.js'
import { Absence } from '../../../absence/entities/absence.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { FullDayAbsence } from '../../../absence/entities/full-day-absence.entity.js'
import { GetDriverAvailabilitiesOnDateUseCase } from './get-driver-availabilities-on-date.use-case.js'
import { GetDriverAvailabilitiesOnDateController } from './get-driver-availabilities-on-date.controller.js'

@Module({
  controllers: [GetDriverAvailabilitiesOnDateController],
  imports: [
    TypeOrmModule.forFeature([
      Shift,
      Absence,
      Driver,
      FullDayAbsence
    ])
  ],
  providers: [GetDriverAvailabilitiesOnDateUseCase]
})
export class GetDriverAvailabilitiesOnDateModule {}
