import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { stringify } from 'qs'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { GetDriverAvailabilitiesQueryBuilder } from '../get-driver-availabilities.query.builder.js'
import { DriverEntityBuilder } from '../../../../driver/entities/driver/driver.entity.builder.js'
import { Driver } from '../../../../driver/entities/driver/driver.entity.js'
import { BranchEntityBuilder } from '../../../../branch/builders/branch.entity.builder.js'
import { Branch } from '../../../../branch/branch.entity.js'
import { ShiftEntityBuilder } from '../../../../shift/builders/shift.entity.builder.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { Shift } from '../../../../shift/entities/shift.entity.js'
import { AbsenceEntityBuilder } from '../../../../absence/builders/absence.entity.builder.js'
import { Absence } from '../../../../absence/entities/absence.entity.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { TypesenseCollectionName } from '../../../../../../modules/typesense/collections/typesense-collection-name.enum.js'
import { TypesenseCollectionService } from '../../../../../../modules/typesense/services/typesense-collection.service.js'
import { MigrateCollectionsUseCase } from '../../../../../../modules/typesense/use-cases/migrate-collections/migrate-collections.use-case.js'

describe('Get driver availabilities e2e test', () => {
  let setup: EndToEndTestSetup
  let adminUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    adminUser = await setup.authContext.getAdminUser()

    const branch = new BranchEntityBuilder().build()
    const location = new LocationBuilder().build()

    const defaultShift1 = new DriverDefaultShiftEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .build()
    const defaultShift2 = new DriverDefaultShiftEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .build()
    const defaultShift3 = new DriverDefaultShiftEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .build()

    const drivers = [
      new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withDefaultShift(defaultShift1)
        .build(),
      new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withDefaultShift(defaultShift2)
        .build(),
      new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withDefaultShift(defaultShift3)
        .build()
    ]

    const shifts = [
      new ShiftEntityBuilder()
        .withFrom(new Date('2024-01-01 10:00:00'))
        .withUntil(new Date('2024-01-01 19:00:00'))
        .withStopLocationUuid(location.uuid)
        .withStartLocationUuid(location.uuid)
        .withDriverUuid(drivers[0].uuid)
        .withBranchUuid(branch.uuid)
        .build()
    ]

    const absences = [
      new AbsenceEntityBuilder()
        .withFrom(new Date('2024-01-02 10:00:00'))
        .withUntil(new Date('2024-01-02 19:00:00'))
        .withDriverUuid(drivers[0].uuid)
        .build()
    ]

    await setup.dataSource.manager.insert(Branch, branch)
    await setup.dataSource.manager.insert(Location, location)
    await setup.dataSource.manager.insert(DriverDefaultShift,
      [defaultShift1, defaultShift2, defaultShift3]
    )
    await setup.dataSource.manager.insert(Driver, drivers)
    await setup.dataSource.manager.insert(Shift, shifts)
    await setup.dataSource.manager.insert(Absence, absences)

    const typesenseMigrator = setup.testModule.get(MigrateCollectionsUseCase)
    const typesenseCollectionService = setup.testModule.get(TypesenseCollectionService)
    await typesenseMigrator.execute(true, [TypesenseCollectionName.DRIVER])
    await typesenseCollectionService.importManually(TypesenseCollectionName.DRIVER, drivers)
  })

  after(async () => await setup.teardown())

  it('returns driver availabilities in a paginated format', async () => {
    const query = new GetDriverAvailabilitiesQueryBuilder()
      .withFrom('2024-01-01')
      .withUntil('2024-01-03')
      .build()

    const response = await request(setup.httpServer)
      .get(`/driver-availabilities`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
