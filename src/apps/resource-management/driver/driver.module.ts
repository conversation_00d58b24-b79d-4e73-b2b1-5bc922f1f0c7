import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DriverUnavailabilityModule } from '../driver-unavailability/driver-unavailability.module.js'
import { Driver } from './entities/driver/driver.entity.js'
import { CreateDriverModule } from './use-cases/create-driver/create-driver.module.js'
import { GetDriversModule } from './use-cases/get-drivers/get-drivers.module.js'
import { UpdateDriverModule } from './use-cases/update-driver/update-driver.module.js'
import { DeleteDriverModule } from './use-cases/delete-driver/delete-driver.module.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([Driver]),
    CreateDriverModule,
    UpdateDriverModule,
    GetDriversModule,
    DeleteDriverModule,
    DriverUnavailabilityModule
  ]
})
export class DriverModule { }
