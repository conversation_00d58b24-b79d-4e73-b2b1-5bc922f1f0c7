import { MoreThanOrEqual, Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { InOrIgnore } from '../../../../utils/typeorm/operators/in-or-ignore.js'
import { Driver } from '../entities/driver/driver.entity.js'
import { TypesenseCollector } from '../../../../modules/typesense/collectors/typesense-collector.js'
import { TypesenseCollectionName } from '../../../../modules/typesense/collections/typesense-collection-name.enum.js'
import { RegisterTypesenseCollector } from '../../../../modules/typesense/collectors/typesense-collector.decorator.js'
import { DriverUuid } from '../entities/driver/driver.uuid.js'
import { TypesenseDriver } from './typesense-driver.js'

@RegisterTypesenseCollector(TypesenseCollectionName.DRIVER)
export class DriverTypesenseCollector implements TypesenseCollector {
  constructor (
    @InjectRepository(Driver) private readonly driverRepository: Repository<Driver>
  ) {}

  transform (drivers: Driver[]): TypesenseDriver[] {
    return drivers.map(driver => new TypesenseDriver(driver))
  }

  async fetch (uuids?: DriverUuid[]): Promise<Driver[]> {
    return await this.driverRepository.find({
      where: { uuid: InOrIgnore(uuids) }
    })
  }

  async fetchChanged (since: Date): Promise<Driver[]> {
    return await this.driverRepository.find({
      where: { updatedAt: MoreThanOrEqual(since) }
    })
  }

  fetchRemoved (_since: Date): Promise<string[]> {
    return Promise.resolve([])
  }

  fetchHidden (_since: Date): Promise<string[]> {
    return Promise.resolve([])
  }
}
