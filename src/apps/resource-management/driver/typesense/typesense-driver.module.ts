import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Driver } from '../entities/driver/driver.entity.js'
import { DriverTypesenseCollector } from './driver-typesense.collector.js'
import { DriverTypesenseCollection } from './driver.collection.js'

@Module({
  imports: [TypeOrmModule.forFeature([Driver])],
  providers: [DriverTypesenseCollector, DriverTypesenseCollection]
})
export class TypesenseDriverModule {}
