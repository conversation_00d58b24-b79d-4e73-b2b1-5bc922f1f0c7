import { Injectable } from '@nestjs/common'
import { PgBossScheduler } from '@wisemen/pgboss-nestjs-job'
import { Subscribe } from '../../../../modules/domain-events/subscribe.decorator.js'
import { DriverUpdatedEvent } from '../use-cases/update-driver/driver-updated.event.js'
import { TypesenseCollectionName } from '../../../../modules/typesense/collections/typesense-collection-name.enum.js'
import { SyncTypesenseJob } from '../../../../modules/typesense/use-cases/sync-collection/sync-typesense-collection.job.js'
import { DriverCreatedEvent } from '../use-cases/create-driver/events/driver-created.event.js'

@Injectable()
export class DriverTypesenseSubscriber {
  constructor (
    private readonly jobScheduler: PgBossScheduler
  ) {}

  @Subscribe(DriverCreatedEvent)
  @Subscribe(DriverUpdatedEvent)
  async handle (): Promise<void> {
    const job = new SyncTypesenseJob(TypesenseCollectionName.DRIVER)
    await this.jobScheduler.scheduleJob(job)
  }
}
