import { Injectable } from '@nestjs/common'
import { <PERSON><PERSON><PERSON>, PgBossScheduler } from '@wisemen/pgboss-nestjs-job'
import { Subscribe } from '../../../../../modules/domain-events/subscribe.decorator.js'
import { CreateNotificationJob } from '../../../../../modules/notification/use-cases/create-notification/create-notification.job.js'
import { NotificationType } from '../../../../../modules/notification/enums/notification-types.enum.js'
import { AuthContext } from '../../../../../modules/auth/auth.context.js'
import { DriverCreatedEvent } from '../../use-cases/create-driver/events/driver-created.event.js'
import { DriverCreatedNotificationMeta } from './driver-created.notification.js'

@Injectable()
export class DriverCreatedNotificationSubscriber {
  constructor (
    private readonly jobScheduler: PgBossScheduler,
    private readonly authContext: AuthContext
  ) {}

  @Subscribe(DriverCreatedEvent)
  async onDriverCreated (events: DriverCreatedEvent[]): Promise<void> {
    const jobs: BaseJob[] = []

    for (const event of events) {
      const content = event.content
      const createdByUserUuid = this.authContext.getUserUuid()

      const job = new CreateNotificationJob({
        createdByUserUuid,
        type: NotificationType.DRIVER_CREATED,
        meta: new DriverCreatedNotificationMeta(content.driverUuid, content.driverName).serialize()
      })
      jobs.push(job)
    }

    await this.jobScheduler.scheduleJobs(jobs)
  }
}
