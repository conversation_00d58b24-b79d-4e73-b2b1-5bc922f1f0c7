import { Column, CreateDateColumn, DeleteDateColumn, Entity, Index, JoinColumn, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn, Relation, UpdateDateColumn } from 'typeorm'
import { Shift } from '../../../shift/entities/shift.entity.js'
import { Branch } from '../../../branch/branch.entity.js'
import { DriverDefaultShift } from '../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { DriverSpeedClass } from '../../enums/driver-speed-class.enum.js'
import { Vehicle } from '../../../vehicle/entities/vehicle.entity.js'
import { Language } from '../../../../../utils/language/language.enum.js'
import { DriverAvailability } from '../driver-availability/driver-availability.entity.js'
import { DriverUnavailability } from '../driver-unavailability/driver-unavailability.entity.js'
import { DriverUuid } from './driver.uuid.js'

@Entity()
export class Driver {
  @PrimaryGeneratedColumn('uuid')
  uuid: DriverUuid

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @Index()
  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @DeleteDateColumn({ type: 'timestamptz' })
  deletedAt: Date | null

  @Column({ type: 'timestamptz', nullable: true })
  deactivatedAt: Date | null

  @Column({ type: 'varchar' })
  firstName: string

  @Column({ type: 'varchar' })
  lastName: string

  @Column({ type: 'varchar', nullable: true })
  email: string | null

  @Column({ type: 'varchar', nullable: true })
  phoneNumber: string | null

  @Column({ type: 'enum', enum: Language, default: Language.NL })
  language: Language

  @Column({ type: 'boolean', default: false })
  driverFlemishLicense: boolean

  @Column({ type: 'uuid', nullable: true })
  invoiceCompanyBranchUuid: string | null

  @ManyToOne(() => Branch, location => location.uuid)
  @JoinColumn({ name: 'invoice_company_branch_uuid' })
  invoiceCompanyBranch?: Relation<Branch>

  @Column({ type: 'enum', enum: DriverSpeedClass, default: DriverSpeedClass.NORMAL })
  speedClass: DriverSpeedClass

  @Column({ type: 'uuid', nullable: true })
  defaultShiftUuid: string | null

  @OneToOne(() => DriverDefaultShift)
  @JoinColumn({ name: 'default_shift_uuid' })
  defaultShift: Relation<DriverDefaultShift>

  @Column({ type: 'uuid', nullable: true })
  defaultVehicleUuid: string | null

  @OneToOne(() => Vehicle)
  @JoinColumn({ name: 'default_vehicle_uuid' })
  defaultVehicle?: Relation<Vehicle>

  @OneToMany(() => Shift, shift => shift.driver)
  shifts: Array<Relation<Shift>>

  @Index()
  @Column({ type: 'uuid' })
  branchUuid: string

  @ManyToOne(() => Branch, branch => branch.uuid)
  @JoinColumn({ name: 'branch_uuid' })
  branch?: Relation<Branch>

  @OneToMany(() => DriverAvailability, availability => availability.driver)
  availabilities?: Array<Relation<DriverAvailability>>

  @OneToMany(() => DriverUnavailability, unavailability => unavailability.driver)
  unavailabilities?: Array<Relation<DriverUnavailability>>

  get fullName (): string {
    return `${this.firstName} ${this.lastName}`
  }
}
