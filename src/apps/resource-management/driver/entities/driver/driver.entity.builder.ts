import { DriverSpeedClass } from '../../enums/driver-speed-class.enum.js'
import { DriverDefaultShift } from '../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { Language } from '../../../../../utils/language/language.enum.js'
import { Driver } from './driver.entity.js'
import { generateDriverUuid, DriverUuid } from './driver.uuid.js'

export class DriverEntityBuilder {
  private readonly driver: Driver = new Driver()

  constructor () {
    this.driver.uuid = generateDriverUuid()
    this.driver.firstName = 'John'
    this.driver.lastName = 'Doe'
    this.driver.email = '<EMAIL>'
    this.driver.phoneNumber = '0472 12 34 56'
    this.driver.language = Language.NL
    this.driver.driverFlemishLicense = false
  }

  withUuid (uuid: DriverUuid): this {
    this.driver.uuid = uuid
    return this
  }

  withFirstName (firstName: string): this {
    this.driver.firstName = firstName
    return this
  }

  withLastName (lastName: string): this {
    this.driver.lastName = lastName
    return this
  }

  withSpeedClass (speedClass: DriverSpeedClass): this {
    this.driver.speedClass = speedClass
    return this
  }

  withBranchUuid (branchUuid: string): this {
    this.driver.branchUuid = branchUuid
    return this
  }

  withDefaultShift (defaultShift: DriverDefaultShift): this {
    this.driver.defaultShift = defaultShift
    return this
  }

  withDefaultShiftUuid (defaultShiftUuid: string): this {
    this.driver.defaultShiftUuid = defaultShiftUuid
    return this
  }

  withDefaultVehicleUuid (defaultVehicleUuid: string): this {
    this.driver.defaultVehicleUuid = defaultVehicleUuid
    return this
  }

  withEmail (email: string): this {
    this.driver.email = email
    return this
  }

  withPhoneNumber (phoneNumber: string): this {
    this.driver.phoneNumber = phoneNumber
    return this
  }

  withLanguage (language: Language): this {
    this.driver.language = language
    return this
  }

  withDriverFlemishLicense (driverFlemishLicense: boolean): this {
    this.driver.driverFlemishLicense = driverFlemishLicense
    return this
  }

  withInvoiceCompanyBranchUuid (invoiceCompanyBranchUuid: string): this {
    this.driver.invoiceCompanyBranchUuid = invoiceCompanyBranchUuid
    return this
  }

  build (): Driver {
    return this.driver
  }
}
