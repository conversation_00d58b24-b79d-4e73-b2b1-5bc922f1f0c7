import { Entity, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, Relation, JoinColumn } from 'typeorm'
import { Column } from 'typeorm'
import { DriverUnavailabilityReasonColumn, DriverUnavailabilityReason } from '../../enums/driver-unavailability.enum.js'
import { Driver } from '../driver/driver.entity.js'
import { DriverUnavailabilityUuid } from './driver-unavailability.uuid.js'

@Entity()
export class DriverUnavailability {
  @PrimaryGeneratedColumn('uuid')
  uuid: DriverUnavailabilityUuid

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Column({ type: 'timestamptz' })
  from: Date

  @Column({ type: 'timestamptz' })
  until: Date

  @DriverUnavailabilityReasonColumn()
  reason: DriverUnavailabilityReason

  @Column({ type: 'uuid' })
  driverUuid: string

  @ManyToOne(() => Driver, driver => driver.availabilities, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'driver_uuid' })
  driver?: Relation<Driver>
}
