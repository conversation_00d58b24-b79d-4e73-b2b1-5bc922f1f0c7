import { DriverUnavailabilityReason } from '../../enums/driver-unavailability.enum.js'
import { DriverUnavailability } from './driver-unavailability.entity.js'
import { generateDriverUnavailabilityUuid } from './driver-unavailability.uuid.js'

export class DriverUnavailabilityEntityBuilder {
  private driverUnavailability: DriverUnavailability

  constructor () {
    this.driverUnavailability = new DriverUnavailability()
    this.driverUnavailability.uuid = generateDriverUnavailabilityUuid()
    this.driverUnavailability.createdAt = new Date()
    this.driverUnavailability.updatedAt = new Date()
    this.driverUnavailability.from = new Date()
    this.driverUnavailability.until = new Date()
    this.driverUnavailability.reason = DriverUnavailabilityReason.ILLNESS
  }

  withDriverUuid (driverUuid: string): this {
    this.driverUnavailability.driverUuid = driverUuid
    return this
  }

  withFrom (from: Date): this {
    this.driverUnavailability.from = from
    return this
  }

  withUntil (until: Date): this {
    this.driverUnavailability.until = until
    return this
  }

  withReason (reason: DriverUnavailabilityReason): this {
    this.driverUnavailability.reason = reason
    return this
  }

  build (): DriverUnavailability {
    return this.driverUnavailability
  }
}
