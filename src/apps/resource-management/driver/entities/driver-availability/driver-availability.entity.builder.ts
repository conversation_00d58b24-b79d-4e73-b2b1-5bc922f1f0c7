import { randomUUID } from 'crypto'
import { DateRange } from '@wisemen/date-range'
import { Timezone } from '../../../../../utils/dates/timezone.enum.js'
import { DriverAvailability, DriverAvailabilitySlot } from './driver-availability.entity.js'

export class DriverAvailabilityEntityBuilder {
  private readonly availability: DriverAvailability = new DriverAvailability()

  constructor () {
    this.availability.uuid = randomUUID()
    this.availability.timezone = Timezone.EUROPE_BRUSSELS
  }

  withSlots (slots: DriverAvailabilitySlot[]): DriverAvailabilityEntityBuilder {
    this.availability.slots = slots
    return this
  }

  withDriverUuid (driverUuid: string): DriverAvailabilityEntityBuilder {
    this.availability.driverUuid = driverUuid
    return this
  }

  withDateRange (daterange: DateRange): DriverAvailabilityEntityBuilder {
    this.availability.daterange = daterange

    return this
  }

  build (): DriverAvailability {
    return this.availability
  }
}
