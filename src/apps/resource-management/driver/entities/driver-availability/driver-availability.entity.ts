import { Entity, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, Relation, JoinColumn, Index } from 'typeorm'
import { Column } from 'typeorm'
import { DateRange, DateRangeColumn } from '@wisemen/date-range'
import { IsoWeekday } from '../../../../../utils/dates/iso-weekday.js'
import { Timezone } from '../../../../../utils/dates/timezone.enum.js'
import { Driver } from '../driver/driver.entity.js'

@Entity()
export class DriverAvailability {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Column({ type: 'int2', default: 1 })
  period: number

  @Column({ type: 'jsonb', default: [] })
  slots: Array<DriverAvailabilitySlot>

  @Column({ type: 'varchar', default: Timezone.EUROPE_BRUSSELS })
  timezone: Timezone

  @Column({ type: 'int2', default: 0 })
  weeklyHoursAvailable: number

  @Column({ type: 'uuid' })
  driverUuid: string

  @ManyToOne(() => Driver, driver => driver.availabilities, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'driver_uuid' })
  driver?: Relation<Driver>

  @Index('driver_availability_daterange_idx', { spatial: true })
  @DateRangeColumn()
  daterange: DateRange
}

export interface DriverAvailabilitySlot {
  startTime: string
  endTime: string
  offset: number
  weekday: IsoWeekday
}
