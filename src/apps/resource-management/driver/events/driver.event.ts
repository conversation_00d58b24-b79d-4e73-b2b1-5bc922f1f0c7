import { DomainEventSubjectType } from '../../../../modules/domain-events/domain-event-subject-type.enum.js'
import { DomainEvent, SubjectedEventOptions } from '../../../../modules/domain-events/domain-event.js'

export class DriverEvent<Content extends object> extends DomainEvent<Content> {
  constructor (options: SubjectedEventOptions<Content, { driverUuid: string }>) {
    super({
      ...options,
      subjectId: options.driverUuid,
      subjectType: DomainEventSubjectType.DRIVER
    })
  }
}
