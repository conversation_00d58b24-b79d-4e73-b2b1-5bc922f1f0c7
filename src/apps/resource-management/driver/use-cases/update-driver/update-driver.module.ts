import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Location } from '../../../../../modules/location/entities/location.entity.js'
import { Driver } from '../../entities/driver/driver.entity.js'
import {
  DriverDefaultShift
} from '../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { UpdateDriverUseCase } from './update-driver.use-case.js'
import { UpdateDriverController } from './update-driver.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Location,
      Driver,
      DriverDefaultShift
    ]),
    DomainEventEmitterModule
  ],
  providers: [
    UpdateDriverUseCase
  ],
  controllers: [
    UpdateDriverController
  ]
})
export class UpdateDriverModule {}
