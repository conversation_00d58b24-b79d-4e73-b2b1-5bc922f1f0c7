import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, stub } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { DriverEntityBuilder } from '../../../entities/driver/driver.entity.builder.js'
import { UpdateDriverCommandBuilder } from '../update-driver-comand.builder.js'
import { UpdateDriverUseCase } from '../update-driver.use-case.js'
import { Driver } from '../../../entities/driver/driver.entity.js'
import { DriverDefaultShift } from '../../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { DriverUpdatedEvent } from '../driver-updated.event.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'

describe('UpdateDriverUseCase Unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('the use cases calls the repository once', async () => {
    const driverRepo = createStubInstance(Repository<Driver>)
    driverRepo.findOneOrFail.resolves(
      new DriverEntityBuilder()
        .withDefaultShiftUuid(randomUUID())
        .build()
    )

    const useCase = new UpdateDriverUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      driverRepo,
      createStubInstance(Repository<DriverDefaultShift>)
    )

    const updateDriver = stub(useCase, 'updateDriver').resolves()

    const command = new UpdateDriverCommandBuilder().build()

    const driver = new DriverEntityBuilder()
      .withFirstName(command.firstName)
      .withLastName(command.lastName)
      .build()

    await useCase.execute(command, driver.uuid)

    assert.calledOnce(updateDriver)
  })

  it('the use cases emits a event driver updated event', async () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)

    const driverRepo = createStubInstance(Repository<Driver>)
    driverRepo.findOneOrFail.resolves(
      new DriverEntityBuilder()
        .withDefaultShiftUuid(randomUUID())
        .build()
    )
    const useCase = new UpdateDriverUseCase(
      stubDataSource(),
      eventEmitter,
      driverRepo,
      createStubInstance(Repository<DriverDefaultShift>)
    )

    const command = new UpdateDriverCommandBuilder().build()

    const driver = new DriverEntityBuilder()
      .withFirstName(command.firstName)
      .withLastName(command.lastName)
      .build()

    await useCase.execute(command, driver.uuid)

    expect(eventEmitter).toHaveEmitted(new DriverUpdatedEvent(driver.uuid))
  })
})
