import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import type { DataSource } from 'typeorm'
import { NestExpressApplication } from '@nestjs/platform-express'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { Branch } from '../../../../branch/branch.entity.js'
import { BranchEntityBuilder } from '../../../../branch/builders/branch.entity.builder.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { DriverEntityBuilder } from '../../../entities/driver/driver.entity.builder.js'
import { UpdateDriverCommandBuilder } from '../update-driver-comand.builder.js'
import { Driver } from '../../../entities/driver/driver.entity.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'

describe('Update driver e2e test', () => {
  let setup: EndToEndTestSetup
  let app: NestExpressApplication
  let dataSource: DataSource
  let adminUser: TestUser
  let branch: Branch
  let driver: Driver
  let defaultShift: DriverDefaultShift
  let location: Location

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    dataSource = setup.dataSource
    app = setup.app

    branch = new BranchEntityBuilder().build()

    location = new LocationBuilder().build()

    defaultShift = new DriverDefaultShiftEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .build()

    driver = new DriverEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withDefaultShiftUuid(defaultShift.uuid)
      .build()

    const result = await Promise.all([
      setup.authContext.getAdminUser(),
      dataSource.manager.insert(Branch, branch),
      dataSource.manager.insert(Location, location)
    ])

    adminUser = result[0]
    await dataSource.manager.insert(DriverDefaultShift, defaultShift)
    await dataSource.manager.insert(Driver, driver)
  })

  after(async () => {
    await setup.teardown()
  })

  it('Update Driver', async () => {
    const response = await request(app.getHttpServer())
      .patch(`/drivers/${driver.uuid}`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(
        new UpdateDriverCommandBuilder()
          .withBranchUuid(branch.uuid)
          .withStartLocationUuid(location.uuid)
          .withStopLocationUuid(location.uuid)
          .build()
      )

    expect(response).toHaveStatus(200)
  })
})
