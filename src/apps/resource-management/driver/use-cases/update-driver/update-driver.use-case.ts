import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DriverDefaultShift } from '../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { Driver } from '../../entities/driver/driver.entity.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { DriverUuid } from '../../entities/driver/driver.uuid.js'
import { UpdateDriverCommand } from './update-driver.command.js'
import { DriverUpdatedEvent } from './driver-updated.event.js'

@Injectable()
export class UpdateDriverUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(Driver) private readonly driverRepository: Repository<Driver>,
    @InjectRepository(DriverDefaultShift)
    private readonly defaultShiftRepository: Repository<DriverDefaultShift>
  ) {}

  async execute (
    command: UpdateDriverCommand,
    driverUuid: DriverUuid
  ): Promise<void> {
    await this.driverRepository.findOneOrFail({
      where: { uuid: driverUuid }
    })
    await transaction(this.dataSource, async () => {
      await this.updateDriver(driverUuid, command)
      await this.eventEmitter.emitOne(new DriverUpdatedEvent(driverUuid))
    })
  }

  async updateDriver (driverUuid: DriverUuid, command: UpdateDriverCommand): Promise<void> {
    await this.driverRepository.update({
      uuid: driverUuid
    }, {
      firstName: command.firstName,
      lastName: command.lastName,
      branchUuid: command.branchUuid,
      email: command.email,
      phoneNumber: command.phoneNumber,
      language: command.language,
      driverFlemishLicense: command.driverFlemishLicense,
      invoiceCompanyBranchUuid: command.invoiceCompanyBranchUuid,
      defaultVehicleUuid: command.defaultVehicleUuid
    })
  }
}
