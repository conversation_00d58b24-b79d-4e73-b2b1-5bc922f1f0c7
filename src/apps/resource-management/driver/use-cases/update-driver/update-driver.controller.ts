import { Body, Controller, Patch } from '@nestjs/common'
import { ApiNoContentResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { DriverUuid } from '../../entities/driver/driver.uuid.js'
import { UpdateDriverUseCase } from './update-driver.use-case.js'
import { UpdateDriverCommand } from './update-driver.command.js'

@ApiTags('Drivers')
@Controller('drivers/:driverUuid')
export class UpdateDriverController {
  constructor (
    private readonly useCase: UpdateDriverUseCase
  ) {}

  @Patch()
  @ApiNoContentResponse({
    description: 'Driver updated'
  })
  async getDriversAvailability (
    @UuidParam('driverUuid') driverUuid: DriverUuid,
    @Body() command: UpdateDriverCommand
  ): Promise<void> {
    await this.useCase.execute(command, driverUuid)
  }
}
