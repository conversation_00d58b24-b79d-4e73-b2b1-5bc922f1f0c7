import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { NestExpressApplication } from '@nestjs/platform-express'
import { stringify } from 'qs'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { Driver } from '../../../entities/driver/driver.entity.js'
import { GetDriversQueryBuilder } from '../query/get-drivers-query.builder.js'
import { DriverEntityBuilder } from '../../../entities/driver/driver.entity.builder.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TypesenseCollectionName } from '../../../../../../modules/typesense/collections/typesense-collection-name.enum.js'
import { TypesenseCollectionService } from '../../../../../../modules/typesense/services/typesense-collection.service.js'
import { MigrateCollectionsUseCase } from '../../../../../../modules/typesense/use-cases/migrate-collections/migrate-collections.use-case.js'

describe('Get drivers - E2e test', () => {
  let testSetup: EndToEndTestSetup
  let app: NestExpressApplication
  let adminUser: TestUser
  let drivers: Driver[]

  before(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    app = testSetup.app

    adminUser = await testSetup.authContext.getAdminUser()

    drivers = [
      new DriverEntityBuilder().build(),
      new DriverEntityBuilder().build(),
      new DriverEntityBuilder().build()
    ]

    const moduleRef = testSetup.testModule

    const typesenseMigrator = moduleRef.get(MigrateCollectionsUseCase)
    const typesenseCollectionService = moduleRef.get(TypesenseCollectionService)
    await typesenseMigrator.execute(true, [TypesenseCollectionName.DRIVER])
    await typesenseCollectionService.importManually(TypesenseCollectionName.DRIVER, drivers)
  })

  after(async () => {
    await testSetup.teardown()
  })

  it('returns all existing drivers in a paginated format when given no query', async () => {
    const response = await request(app.getHttpServer())
      .get(`/drivers/search`)
      .set('Authorization', `Bearer ${adminUser.token}`)

    expect(response).toHaveStatus(200)
    expect(response.body.meta.total).toBe(3)
    expect(response.body.items).toHaveLength(3)
  })

  it('excludes a driver from the result', async () => {
    const query = new GetDriversQueryBuilder()
      .withExcludedDriverUuids([drivers[0].uuid])
      .build()

    const response = await request(app.getHttpServer())
      .get(`/drivers/search`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.meta.total).toBe(2)
    expect(response.body.items).toHaveLength(2)
  })
})
