import { ApiProperty } from '@nestjs/swagger'
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Min,
  ValidateNested
} from 'class-validator'
import { Type } from 'class-transformer'
import { LocationUuid } from '../../../../../../modules/location/entities/location.uuid.js'
import { Language, LanguageApiProperty } from '../../../../../../utils/language/language.enum.js'
import { CreateDriverAvailabilityCommand } from './create-driver-availability.command.js'

export class CreateDriverCommand {
  @ApiProperty({ type: 'string' })
  @IsString()
  @IsNotEmpty()
  firstName: string

  @ApiProperty({ type: 'string' })
  @IsString()
  @IsNotEmpty()
  lastName: string

  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  @IsEmail()
  email: string

  @ApiProperty({ type: 'string' })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string

  @ApiProperty({ type: 'string' })
  @IsString()
  @IsNotEmpty()
  licensePlate: string

  @ApiProperty({ type: 'number', minimum: 1 })
  @IsNotEmpty()
  @Min(1)
  capacity: number

  @ApiProperty({ type: 'string', format: 'uuid' })
  @IsUUID()
  branchUuid: string

  @ApiProperty({ type: String, format: 'uuid', required: false })
  @IsUUID()
  @IsOptional()
  invoiceCompanyBranchUuid?: string

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  startLocationUuid: LocationUuid

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  stopLocationUuid: LocationUuid

  @LanguageApiProperty()
  @IsEnum(Language)
  language: Language

  @ApiProperty({ type: 'boolean' })
  @IsBoolean()
  driverFlemishLicense: boolean

  @ApiProperty({ type: String, format: 'uuid', required: false })
  @IsUUID()
  @IsOptional()
  defaultVehicleUuid?: string

  @ApiProperty({ type: CreateDriverAvailabilityCommand })
  @Type(() => CreateDriverAvailabilityCommand)
  @ValidateNested()
  availability: CreateDriverAvailabilityCommand
}
