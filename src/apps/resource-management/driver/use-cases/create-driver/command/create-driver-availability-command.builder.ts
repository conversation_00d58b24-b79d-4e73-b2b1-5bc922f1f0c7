import { IsoWeekday } from '../../../../../../utils/dates/iso-weekday.js'
import { Timezone } from '../../../../../../utils/dates/timezone.enum.js'
import { CreateDriverAvailabilityCommand, DriverAvailabilitySlot } from './create-driver-availability.command.js'

export class CreateDriverAvailabilityCommandBuilder {
  private command: CreateDriverAvailabilityCommand

  constructor () {
    const defaultSlot: DriverAvailabilitySlot = {
      startTime: '08:00:00',
      endTime: '17:00:00',
      offset: 0,
      weekday: IsoWeekday.MONDAY
    }

    this.command = new CreateDriverAvailabilityCommand()
    this.command.timezone = Timezone.EUROPE_BRUSSELS
    this.command.period = 1
    this.command.slots = [defaultSlot]
  }

  withTimezone (timezone: Timezone): CreateDriverAvailabilityCommandBuilder {
    this.command.timezone = timezone
    return this
  }

  withPeriod (weeks: number): CreateDriverAvailabilityCommandBuilder {
    this.command.period = weeks
    return this
  }

  withSlots (slots: DriverAvailabilitySlot[]): CreateDriverAvailabilityCommandBuilder {
    this.command.slots = slots
    return this
  }

  withWeeklyHoursAvailable (hours: number): CreateDriverAvailabilityCommandBuilder {
    this.command.weeklyHoursAvailable = hours
    return this
  }

  build (): CreateDriverAvailabilityCommand {
    return this.command
  }
}
