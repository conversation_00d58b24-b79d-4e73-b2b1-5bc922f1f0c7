import { generateLocationUuid, LocationUuid } from '../../../../../../modules/location/entities/location.uuid.js'
import { Language } from '../../../../../../utils/language/language.enum.js'
import { CreateDriverAvailabilityCommandBuilder } from './create-driver-availability-command.builder.js'
import { CreateDriverAvailabilityCommand } from './create-driver-availability.command.js'
import { CreateDriverCommand } from './create-driver.command.js'

export class CreateDriverCommandBuilder {
  private command: CreateDriverCommand
  constructor () {
    this.command = new CreateDriverCommand()
    this.command.firstName = 'John'
    this.command.lastName = 'Doe'
    this.command.licensePlate = '1-abc-123'
    this.command.capacity = 1
    this.command.startLocationUuid = generateLocationUuid()
    this.command.stopLocationUuid = generateLocationUuid()
    this.command.email = '<EMAIL>'
    this.command.phoneNumber = '0472 12 34 56'
    this.command.language = Language.NL
    this.command.driverFlemishLicense = false
    this.command.availability = new CreateDriverAvailabilityCommandBuilder().build()
  }

  withFirstName (firstName: string): CreateDriverCommandBuilder {
    this.command.firstName = firstName
    return this
  }

  withLastName (lastName: string): CreateDriverCommandBuilder {
    this.command.lastName = lastName
    return this
  }

  withLicensePlate (licensePlate: string): CreateDriverCommandBuilder {
    this.command.licensePlate = licensePlate
    return this
  }

  withStartLocationUuid (uuid: LocationUuid): CreateDriverCommandBuilder {
    this.command.startLocationUuid = uuid
    return this
  }

  withStopLocationUuid (uuid: LocationUuid): CreateDriverCommandBuilder {
    this.command.stopLocationUuid = uuid
    return this
  }

  withBranchUuid (branchUuid: string): CreateDriverCommandBuilder {
    this.command.branchUuid = branchUuid
    return this
  }

  withPhoneNumber (phoneNumber: string): CreateDriverCommandBuilder {
    this.command.phoneNumber = phoneNumber
    return this
  }

  withEmail (email: string): CreateDriverCommandBuilder {
    this.command.email = email
    return this
  }

  withLanguage (language: Language): CreateDriverCommandBuilder {
    this.command.language = language
    return this
  }

  withDriverFlemishLicense (driverFlemishLicense: boolean): CreateDriverCommandBuilder {
    this.command.driverFlemishLicense = driverFlemishLicense
    return this
  }

  withInvoiceCompanyBranchUuid (invoiceCompanyBranchUuid: string): CreateDriverCommandBuilder {
    this.command.invoiceCompanyBranchUuid = invoiceCompanyBranchUuid
    return this
  }

  withDefaultVehicleUuid (defaultVehicleUuid: string): CreateDriverCommandBuilder {
    this.command.defaultVehicleUuid = defaultVehicleUuid
    return this
  }

  withAvailability (availability: CreateDriverAvailabilityCommand): CreateDriverCommandBuilder {
    this.command.availability = availability
    return this
  }

  build (): CreateDriverCommand {
    return this.command
  }
}
