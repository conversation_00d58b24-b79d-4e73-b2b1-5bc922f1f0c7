import { ApiProperty } from '@nestjs/swagger'
import { IsTimeString } from '@wisemen/validators'
import { Type } from 'class-transformer'
import { IsEnum, IsNotEmpty, IsNumber, Min, ValidateNested } from 'class-validator'
import { IsoWeekday, IsoWeekdayApiProperty } from '../../../../../../utils/dates/iso-weekday.js'
import { Timezone, TimezoneApiProperty } from '../../../../../../utils/dates/timezone.enum.js'

export class DriverAvailabilitySlot {
  @ApiProperty({ type: 'string', format: 'time' })
  @IsTimeString()
  startTime: string

  @ApiProperty({ type: 'string', format: 'time' })
  @IsTimeString()
  endTime: string

  @ApiProperty({ type: 'number', description: 'Offset week for schedule (0 = first week, 1 = following week, ...)' })
  @IsNumber()
  offset: number

  @IsoWeekdayApiProperty()
  @IsEnum(IsoWeekday)
  weekday: IsoWeekday
}

export class CreateDriverAvailabilityCommand {
  @TimezoneApiProperty()
  @IsEnum(Timezone)
  timezone: Timezone

  @ApiProperty({ type: 'number', description: 'Number of weeks for which the availability is valid (1 weekly, 2 weekly)' })
  @IsNumber()
  period: number

  @ApiProperty({ type: DriverAvailabilitySlot, isArray: true })
  @Type(() => DriverAvailabilitySlot)
  @ValidateNested({ each: true })
  @IsNotEmpty()
  slots: DriverAvailabilitySlot[]

  @ApiProperty({ type: 'number', minimum: 0 })
  @Min(0)
  @IsNumber()
  weeklyHoursAvailable: number
}
