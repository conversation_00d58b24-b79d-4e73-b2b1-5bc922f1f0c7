import { ApiProperty } from '@nestjs/swagger'
import { Driver } from '../../entities/driver/driver.entity.js'

export class CreateDriverResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  firstName: string

  @ApiProperty({ type: String })
  lastName: string

  constructor (driver: Driver) {
    this.uuid = driver.uuid
    this.firstName = driver.firstName
    this.lastName = driver.lastName
  }
}
