import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEvent } from '../../../../../modules/domain-events/domain-event.js'
import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'
import { DriverUuid } from '../../entities/driver/driver.uuid.js'

@OneOfMeta(DomainEventLog, DomainEventType.DRIVER_CREATED)
export class DriverCreatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly driverUuid: DriverUuid

  @ApiProperty({ format: 'date-time' })
  readonly lastSeededDate: Date

  @ApiProperty()
  readonly driverName: string

  constructor (
    driverUuid: DriverUuid,
    lastSeededDate: Date,
    driverName: string
  ) {
    this.driverUuid = driverUuid
    this.lastSeededDate = lastSeededDate
    this.driverName = driverName
  }
}

@RegisterDomainEvent(DomainEventType.DRIVER_CREATED, 1)
export class DriverCreatedEvent extends DomainEvent<DriverCreatedEventContent> {
  constructor (driverUuid: DriverUuid, lastSeededDate: Date, driverName: string) {
    super({ content: new DriverCreatedEventContent(driverUuid, lastSeededDate, driverName) })
  }
}
