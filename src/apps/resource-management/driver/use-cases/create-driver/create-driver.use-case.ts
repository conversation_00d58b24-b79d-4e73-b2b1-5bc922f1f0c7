import { Injectable } from '@nestjs/common'
import { Any, DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DateRange } from '@wisemen/date-range'
import { FutureInfinityDate, WiseDate } from '@wisemen/wise-date'
import { Location } from '../../../../../modules/location/entities/location.entity.js'
import {
  LocationNotFoundApiError
} from '../../../../../modules/location/location-not-found.api-error.js'
import { Driver } from '../../entities/driver/driver.entity.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'

import { DriverAvailability } from '../../entities/driver-availability/driver-availability.entity.js'
import { DriverAvailabilityCreatedEvent } from './events/driver-availability-created.event.js'
import { CreateDriverAvailabilityCommand } from './command/create-driver-availability.command.js'
import { CreateDriverResponse } from './create-driver.response.js'
import { CreateDriverCommand } from './command/create-driver.command.js'
import { DriverCreatedEvent } from './events/driver-created.event.js'

@Injectable()
export class CreateDriverUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(Driver) private readonly driverRepo: Repository<Driver>,
    @InjectRepository(DriverAvailability)
    private readonly driverAvailabilityRepo: Repository<DriverAvailability>,
    @InjectRepository(Location) private readonly locationRepo: Repository<Location>
  ) {}

  async execute (
    command: CreateDriverCommand
  ): Promise<CreateDriverResponse> {
    await this.assertLocationsExist(command)

    const driver = await transaction(this.dataSource, async () => {
      const driver = await this.createDriver(command)
      const availability = await this.createAvailability(driver.uuid, command.availability)

      const driverEvent = new DriverCreatedEvent(driver.uuid, driver.fullName)
      const availabilityEvent = new DriverAvailabilityCreatedEvent(availability.uuid, driver.uuid)

      await this.eventEmitter.emit([
        driverEvent,
        availabilityEvent
      ])

      return driver
    })

    return new CreateDriverResponse(driver)
  }

  private async createDriver (
    command: CreateDriverCommand
  ): Promise<Driver> {
    const driver = this.driverRepo.create({
      firstName: command.firstName,
      lastName: command.lastName,
      branchUuid: command.branchUuid,
      phoneNumber: command.phoneNumber,
      email: command.email,
      language: command.language,
      driverFlemishLicense: command.driverFlemishLicense,
      invoiceCompanyBranchUuid: command.invoiceCompanyBranchUuid,
      defaultVehicleUuid: command.defaultVehicleUuid
    })
    await this.driverRepo.insert(driver)

    return driver
  }

  private async createAvailability (
    driverUuid: string,
    command: CreateDriverAvailabilityCommand
  ): Promise<DriverAvailability> {
    const availability = this.driverAvailabilityRepo.create({
      driverUuid,
      daterange: new DateRange(
        WiseDate.today(),
        new FutureInfinityDate()
      ),
      slots: command.slots,
      period: command.period,
      timezone: command.timezone,
      weeklyHoursAvailable: command.weeklyHoursAvailable
    })

    await this.driverAvailabilityRepo.insert(availability)

    return availability
  }

  private async assertLocationsExist (command: CreateDriverCommand): Promise<void> {
    const locationUuids = [command.startLocationUuid, command.stopLocationUuid]

    const locations = await this.locationRepo.findBy({ uuid: Any(locationUuids) })

    for (const locationUuid of locationUuids) {
      const locationExists = locations.some(location => location.uuid === locationUuid)
      if (!locationExists) {
        throw new LocationNotFoundApiError(locationUuid)
      }
    }
  }
}
