import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEventLog } from '../../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { DomainEventType } from '../../../../../../modules/domain-events/domain-event-type.js'
import { DomainEvent } from '../../../../../../modules/domain-events/domain-event.js'
import { RegisterDomainEvent } from '../../../../../../modules/domain-events/register-domain-event.decorator.js'

@OneOfMeta(DomainEventLog, DomainEventType.DRIVER_CREATED)
export class DriverCreatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly driverUuid: string

  @ApiProperty()
  readonly driverName: string

  constructor (
    driverUuid: string,
    driverName: string
  ) {
    this.driverUuid = driverUuid
    this.driverName = driverName
  }
}

@RegisterDomainEvent(DomainEventType.DRIVER_CREATED, 1)
export class DriverCreatedEvent extends DomainEvent<DriverCreatedEventContent> {
  constructor (driverUuid: string, driverName: string) {
    super({ content: new DriverCreatedEventContent(driverUuid, driverName) })
  }
}
