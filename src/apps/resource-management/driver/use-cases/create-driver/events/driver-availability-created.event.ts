import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEventLog } from '../../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { DomainEventType } from '../../../../../../modules/domain-events/domain-event-type.js'
import { RegisterDomainEvent } from '../../../../../../modules/domain-events/register-domain-event.decorator.js'
import { DriverEvent } from '../../../events/driver.event.js'

@OneOfMeta(DomainEventLog, DomainEventType.DRIVER_AVAILABILITY_CREATED)
export class DriverAvailibilityCreatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly driverAvailabilityUuid: string

  @ApiProperty({ format: 'uuid' })
  readonly driverUuid: string

  constructor (
    driverAvailabilityUuid: string,
    driverUuid: string
  ) {
    this.driverAvailabilityUuid = driverAvailabilityUuid
    this.driverUuid = driverUuid
  }
}

@RegisterDomainEvent(DomainEventType.DRIVER_AVAILABILITY_CREATED, 1)
export class DriverAvailabilityCreatedEvent extends
  DriverEvent<DriverAvailibilityCreatedEventContent> {
  constructor (
    driverAvailabilityUuid: string,
    driverUuid: string
  ) {
    super({
      driverUuid,
      content: new DriverAvailibilityCreatedEventContent(driverAvailabilityUuid, driverUuid)
    })
  }
}
