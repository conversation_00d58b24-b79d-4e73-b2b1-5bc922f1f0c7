import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Driver } from '../../entities/driver/driver.entity.js'
import { Location } from '../../../../../modules/location/entities/location.entity.js'

import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { DriverAvailability } from '../../entities/driver-availability/driver-availability.entity.js'
import { CreateDriverUseCase } from './create-driver.use-case.js'
import { CreateDriverController } from './create-driver.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Driver,
      Location,
      DriverAvailability
    ]),
    DomainEventEmitterModule
  ],
  providers: [
    CreateDriverUseCase
  ],
  controllers: [
    CreateDriverController
  ]
})
export class CreateDriverModule {}
