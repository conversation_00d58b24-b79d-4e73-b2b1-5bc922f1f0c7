import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import type { DataSource } from 'typeorm'
import { NestExpressApplication } from '@nestjs/platform-express'
import { HttpStatus } from '@nestjs/common'
import { Branch } from '../../../../branch/branch.entity.js'
import { BranchEntityBuilder } from '../../../../branch/builders/branch.entity.builder.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { CreateDriverCommandBuilder } from '../command/create-driver-command.builder.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { BranchUuid } from '../../../../branch/branch.uuid.js'
import { CreateDriverAvailabilityCommandBuilder } from '../command/create-driver-availability-command.builder.js'
import { Timezone } from '../../../../../../utils/dates/timezone.enum.js'

describe('Create Driver Availabilities e2e test', () => {
  let setup: EndToEndTestSetup
  let app: NestExpressApplication
  let dataSource: DataSource
  let adminUser: TestUser
  let branch: Branch
  let location: Location

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    dataSource = setup.dataSource
    app = setup.app

    adminUser = await setup.authContext.getAdminUser()

    location = new LocationBuilder().build()

    branch = new BranchEntityBuilder().withUuid('931350cb-c804-5860-9f49-1a4083fb698a' as BranchUuid).build()

    const result = await Promise.all([
      await setup.authContext.getAdminUser(),
      dataSource.manager.insert(Location, location),
      dataSource.manager.insert(Branch, branch)
    ])
    adminUser = result[0]
  })

  after(async () => {
    await setup.teardown()
  })

  it('Creates Driver', async () => {
    const availability = new CreateDriverAvailabilityCommandBuilder()
      .withSlots([
        {
          startTime: '08:00:00',
          endTime: '12:00:00',
          offset: 0,
          weekday: 1
        },
        {
          startTime: '13:00:00',
          endTime: '17:00:00',
          offset: 0,
          weekday: 1
        }
      ])
      .withTimezone(Timezone.EUROPE_BRUSSELS)
      .withPeriod(1)
      .withWeeklyHoursAvailable(40)
      .build()

    const response = await request(app.getHttpServer())
      .post('/drivers')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(new CreateDriverCommandBuilder()
        .withBranchUuid(branch.uuid)
        .withStartLocationUuid(location.uuid)
        .withStopLocationUuid(location.uuid)
        .withAvailability(availability)
        .build())

    expect(response).toHaveStatus(HttpStatus.CREATED)
  })
})
