import { before, describe, it } from 'node:test'
import { createStubInstance, assert } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { CreateDriverUseCase } from '../create-driver.use-case.js'
import { Driver } from '../../../entities/driver/driver.entity.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { CreateDriverCommandBuilder } from '../command/create-driver-command.builder.js'
import { DriverEntityBuilder } from '../../../entities/driver/driver.entity.builder.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { DriverAvailabilityCreatedEvent } from '../events/driver-availability-created.event.js'
import { DriverCreatedEvent } from '../events/driver-created.event.js'
import { DriverAvailabilityEntityBuilder } from '../../../entities/driver-availability/driver-availability.entity.builder.js'
import { DriverAvailability } from '../../../entities/driver-availability/driver-availability.entity.js'

describe('CreateDriverUseCase Unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('the use cases calls the repository once', async () => {
    const command = new CreateDriverCommandBuilder().build()

    const driver = new DriverEntityBuilder()
      .withFirstName(command.firstName)
      .withLastName(command.lastName)
      .build()

    const driverRepo = createStubInstance(Repository<Driver>)
    driverRepo.create.returns(driver)

    const driverAvailabilityRepo = createStubInstance(Repository<DriverAvailability>)
    driverAvailabilityRepo.create.returns({})

    const locationsRepository = createStubInstance(Repository<Location>)
    locationsRepository.findBy.resolves([
      new LocationBuilder().withUuid(command.startLocationUuid).build(),
      new LocationBuilder().withUuid(command.stopLocationUuid).build()
    ])

    const useCase = new CreateDriverUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      driverRepo,
      driverAvailabilityRepo,
      locationsRepository
    )

    await useCase.execute(command)

    assert.calledOnce(driverRepo.create)
  })

  it('the use cases emits a driver and availability created event', async () => {
    const command = new CreateDriverCommandBuilder().build()

    const driver = new DriverEntityBuilder()
      .withFirstName(command.firstName)
      .withLastName(command.lastName)
      .build()

    const availability = new DriverAvailabilityEntityBuilder()
      .withSlots([
        {
          startTime: '08:00:00',
          endTime: '12:00:00',
          offset: 0,
          weekday: 1
        },
        {
          startTime: '13:00:00',
          endTime: '17:00:00',
          offset: 0,
          weekday: 1
        }
      ])
      .build()

    const driverRepo = createStubInstance(Repository<Driver>)
    driverRepo.create.returns(driver)

    const driverAvailabilityRepo = createStubInstance(Repository<DriverAvailability>)
    driverAvailabilityRepo.create.returns(availability)

    const locationsRepository = createStubInstance(Repository<Location>)
    locationsRepository.findBy.resolves([
      new LocationBuilder().withUuid(command.startLocationUuid).build(),
      new LocationBuilder().withUuid(command.stopLocationUuid).build()
    ])

    const eventEmitter = createStubInstance(DomainEventEmitter)

    const useCase = new CreateDriverUseCase(
      stubDataSource(),
      eventEmitter,
      driverRepo,
      driverAvailabilityRepo,
      locationsRepository
    )

    await useCase.execute(command)

    const driverCreatedEvent = new DriverCreatedEvent(driver.uuid, driver.fullName)
    const driverAvailabilityCreatedEvent
      = new DriverAvailabilityCreatedEvent(availability.uuid, driver.uuid)

    expect(eventEmitter).toHaveEmitted(driverCreatedEvent, driverAvailabilityCreatedEvent)
  })
})
