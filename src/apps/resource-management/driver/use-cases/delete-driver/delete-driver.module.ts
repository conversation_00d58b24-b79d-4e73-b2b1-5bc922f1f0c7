import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Driver } from '../../entities/driver/driver.entity.js'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { DeleteDriverUseCase } from './delete-driver.use-case.js'
import { DeleteDriverController } from './delete-driver.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([Driver]),
    DomainEventEmitterModule
  ],
  controllers: [
    DeleteDriverController
  ],
  providers: [
    DeleteDriverUseCase
  ]
})
export class DeleteDriverModule { }
