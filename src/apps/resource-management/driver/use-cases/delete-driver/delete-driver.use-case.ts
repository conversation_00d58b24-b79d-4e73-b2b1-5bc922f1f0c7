import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import { Driver } from '../../entities/driver/driver.entity.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { DriverUuid } from '../../entities/driver/driver.uuid.js'
import { DriverDeletedEvent } from './driver-deleted.event.js'
import { DeleteDriverQuery } from './delete-driver.query.js'
import { PastDeactivatedAtError } from './errors/past-deactivated-at.error.js'

@Injectable()
export class DeleteDriverUseCase {
  constructor (
    @InjectRepository(Driver)
    private driverRepository: Repository<Driver>,
    private domainEventEmitter: DomainEventEmitter,
    private dataSource: DataSource
  ) {}

  public async execute (
    uuid: DriverUuid,
    query: DeleteDriverQuery
  ): Promise<void> {
    this.verify(query)

    await transaction(this.dataSource, async () => {
      await this.driverRepository.softDelete({
        uuid,
        deactivatedAt: new Date(query.deactivatedAt)
      })
      await this.domainEventEmitter.emitOne(new DriverDeletedEvent(uuid))
    })
  }

  private verify (query: DeleteDriverQuery): void {
    if (new Date(query.deactivatedAt) < new Date()) {
      throw new PastDeactivatedAtError()
    }
  }
}
