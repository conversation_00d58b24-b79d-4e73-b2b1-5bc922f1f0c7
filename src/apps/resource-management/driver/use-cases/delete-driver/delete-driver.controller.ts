import { Controller, Delete, Query } from '@nestjs/common'
import { ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permission } from '../../../../../modules/permission/permission.enum.js'
import { Permissions } from '../../../../../modules/permission/permission.decorator.js'
import { ApiBadRequestErrorResponse } from '../../../../../modules/exceptions/api-errors/api-error-response.decorator.js'
import { DriverUuid } from '../../entities/driver/driver.uuid.js'
import { DeleteDriverUseCase } from './delete-driver.use-case.js'
import { DeleteDriverQuery } from './delete-driver.query.js'
import { PastDeactivatedAtError } from './errors/past-deactivated-at.error.js'

@ApiTags('Driver')
@ApiOAuth2([])
@Controller('drivers/:uuid')
export class DeleteDriverController {
  constructor (
    private readonly deleteDriverUseCase: DeleteDriverUseCase
  ) { }

  @Delete()
  @Permissions(Permission.DRIVER_DELETE)
  @ApiBadRequestErrorResponse(PastDeactivatedAtError)
  public async deleteDriver (
    @UuidParam('uuid') uuid: DriverUuid,
    @Query() query: DeleteDriverQuery
  ): Promise<void> {
    await this.deleteDriverUseCase.execute(uuid, query)
  }
}
