import { before, describe, it, after } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import type { DataSource } from 'typeorm'
import dayjs from 'dayjs'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestAuthContext } from '../../../../../../../test/utils/test-auth-context.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { Driver } from '../../../entities/driver/driver.entity.js'
import { DriverEntityBuilder } from '../../../entities/driver/driver.entity.builder.js'
import { BranchEntityBuilder } from '../../../../branch/branch.entity-builder.js'
import { Branch } from '../../../../branch/branch.entity.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { LocationEntityBuilder } from '../../../../../../modules/location/entities/location.entity-builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { AddressBuilder } from '../../../../../../utils/address/address.builder.js'
import { DeleteDriverQueryBuilder } from '../delete-driver.query-builder.js'

describe('Delete driver end to end tests', () => {
  let setup: EndToEndTestSetup
  let dataSource: DataSource
  let context: TestAuthContext
  let adminUser: TestUser
  let defaultUser: TestUser

  let driver: Driver

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    context = setup.authContext
    dataSource = setup.dataSource

    adminUser = await context.getAdminUser()
    defaultUser = await context.getDefaultUser()

    const location = new LocationEntityBuilder()
      .withAddress(new AddressBuilder().build())
      .build()
    const branch = new BranchEntityBuilder().build()
    const shift = new DriverDefaultShiftEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withStopLocationUuid(location.uuid)
      .build()

    driver = new DriverEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withDefaultShiftUuid(shift.uuid)
      .build()

    await dataSource.manager.save(Location, location)
    await dataSource.manager.save(Branch, branch)
    await dataSource.manager.save(DriverDefaultShift, shift)
    await dataSource.manager.save(Driver, driver)
  })

  after(async () => await setup.teardown())

  describe('Delete driver', () => {
    it('should return 401 when not authenticated', async () => {
      const response = await request(setup.httpServer)
        .delete(`/drivers/${driver.uuid}`)

      expect(response).toHaveStatus(401)
    })

    it('should return 403 when not authorized', async () => {
      const response = await request(setup.httpServer)
        .delete(`/drivers/${driver.uuid}`)
        .set('Authorization', `Bearer ${defaultUser.token}`)

      expect(response).toHaveStatus(403)
    })

    it('should delete driver', async () => {
      const query = new DeleteDriverQueryBuilder()
        .withDeactivatedAt(dayjs().add(1, 'hour').toISOString())
        .build()

      const response = await request(setup.httpServer)
        .delete(`/drivers/${driver.uuid}`)
        .query(query)
        .set('Authorization', `Bearer ${adminUser.token}`)

      expect(response).toHaveStatus(200)
    })

    it('should return 400 when deactivatedAt is in the past', async () => {
      const query = new DeleteDriverQueryBuilder()
        .withDeactivatedAt(dayjs().subtract(1, 'hour').toISOString())
        .build()

      const response = await request(setup.httpServer)
        .delete(`/drivers/${driver.uuid}`)
        .query(query)
        .set('Authorization', `Bearer ${adminUser.token}`)

      expect(response).toHaveStatus(400)
    })
  })
})
