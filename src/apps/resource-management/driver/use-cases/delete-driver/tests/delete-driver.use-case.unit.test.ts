import { before, describe, it } from 'node:test'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { DriverEntityBuilder } from '../../../entities/driver/driver.entity.builder.js'
import { Driver } from '../../../entities/driver/driver.entity.js'
import { DeleteDriverUseCase } from '../delete-driver.use-case.js'
import { DriverDeletedEvent } from '../driver-deleted.event.js'
import { DeleteDriverQueryBuilder } from '../delete-driver.query-builder.js'

describe('DeleteDriverUseCase Unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('emits an event when deleting a vehicle', async () => {
    const vehicle = new DriverEntityBuilder().build()

    const repository = createStubInstance(Repository<Driver>)
    repository.softDelete.resolves()

    const eventEmitter = createStubInstance(DomainEventEmitter)

    const useCase = new DeleteDriverUseCase(repository, eventEmitter, stubDataSource())

    const query = new DeleteDriverQueryBuilder().build()

    await expect(useCase.execute(vehicle.uuid, query)).resolves.toBeUndefined()
    expect(eventEmitter).toHaveEmitted(new DriverDeletedEvent(vehicle.uuid))
  })
})
