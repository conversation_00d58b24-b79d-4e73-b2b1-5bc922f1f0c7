import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn
} from 'typeorm'
import { Driver } from '../../driver/entities/driver/driver.entity.js'
import { Branch } from '../../branch/branch.entity.js'
import { Location } from '../../../../modules/location/entities/location.entity.js'
import {
  Duration,
  DurationColumn
} from '../../../../utils/quantities/duration.js'
import { DEFAULT_MAX_SHIFT_DURATION, DEFAULT_SHIFT_CAPACITY } from '../constants.js'

@Entity()
@Index(['driverUuid', 'from', 'until'])
export class Shift {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @DeleteDateColumn({ type: 'timestamptz' })
  deletedAt: Date | null

  @Index()
  @Column({ type: 'timestamptz' })
  from: Date

  @Column({ type: 'timestamptz' })
  until: Date

  @Column({ type: 'varchar' })
  licensePlate: string

  @Column({ type: 'uuid' })
  startLocationUuid: string

  @ManyToOne(() => Location)
  @JoinColumn({ name: 'start_location_uuid' })
  startLocation?: Relation<Location>

  @Column({ type: 'uuid' })
  stopLocationUuid: string

  @ManyToOne(() => Location)
  @JoinColumn({ name: 'stop_location_uuid' })
  stopLocation?: Relation<Location>

  @Column({ type: 'int', default: DEFAULT_SHIFT_CAPACITY })
  capacity: number

  @DurationColumn({ default: DEFAULT_MAX_SHIFT_DURATION.valueOf() })
  maxDuration: Duration

  @Index()
  @Column({ type: 'uuid' })
  driverUuid: string

  @ManyToOne(() => Driver, driver => driver.uuid)
  @JoinColumn({ name: 'driver_uuid' })
  driver?: Relation<Driver>

  @Column({ type: 'uuid' })
  branchUuid: string

  @ManyToOne(() => Branch, branch => branch.uuid)
  @JoinColumn({ name: 'branch_uuid' })
  branch?: Relation<Branch>

  @Column({ type: 'boolean', default: false })
  isHidden: boolean
}
