import { EntityManager } from 'typeorm'
import { Branch } from '../../../../branch/branch.entity.js'
import { BranchEntityBuilder } from '../../../../branch/builders/branch.entity.builder.js'
import { DriverEntityBuilder } from '../../../../driver/entities/driver/driver.entity.builder.js'
import { Driver } from '../../../../driver/entities/driver/driver.entity.js'
import { ShiftEntityBuilder } from '../../../builders/shift.entity.builder.js'
import { Shift } from '../../../entities/shift.entity.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../driver-default-shift/entities/driver-default-shift.entity.js'

export interface UpdateShiftTestContext {
  existingShift: Shift
  location: Location
}

export async function setupUpdateShiftContext (
  entityManager: EntityManager
): Promise<UpdateShiftTestContext> {
  const branch = new BranchEntityBuilder().build()
  const location = new LocationBuilder().build()

  const defaultShift = new DriverDefaultShiftEntityBuilder()
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const driver = new DriverEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withDefaultShiftUuid(defaultShift.uuid)
    .build()

  const existingShift = new ShiftEntityBuilder()
    .withDriverUuid(driver.uuid)
    .withBranchUuid(branch.uuid)
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  await Promise.all([
    entityManager.insert(Branch, branch),
    entityManager.insert(Location, location)
  ])
  await entityManager.insert(DriverDefaultShift, defaultShift)
  await entityManager.insert(Driver, driver)
  await entityManager.insert(Shift, existingShift)

  return {
    existingShift,
    location: location
  }
}
