import { before, describe, it } from 'node:test'
import { createStubInstance } from 'sinon'
import dayjs from 'dayjs'
import { expect } from 'expect'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { SeedDriverShiftsRepository } from '../seed-driver-shifts.repository.js'
import { DriverEntityBuilder } from '../../../../driver/entities/driver/driver.entity.builder.js'
import { SeedDriverShiftsUseCase } from '../seed-driver-shifts.use-case.js'
import { DriverDoesNotHaveDefaultShift } from '../../../errors/driver-does-not-have-default-shift.error.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { ShiftEntityBuilder } from '../../../builders/shift.entity.builder.js'
import { combineWithTime } from '../../../../../../utils/dates/combine-dayjs-with-time.helper.js'
import { DEFAULT_MAX_SHIFT_DURATION } from '../../../constants.js'
import { generateDriverUuid } from '../../../../driver/entities/driver/driver.uuid.js'

describe('SeedDriverShiftsUseCase - Unit tests', () => {
  before(() => TestBench.setupUnitTest())

  it('Should throw an error when the driver does not have default shift', async () => {
    const repo = createStubInstance(SeedDriverShiftsRepository)
    repo.getDriver.resolves(
      new DriverEntityBuilder()
        .build()
    )

    const useCase = new SeedDriverShiftsUseCase(repo)
    const driverUuid = generateDriverUuid()
    const lastSeededDate = dayjs()

    const expectedError = new DriverDoesNotHaveDefaultShift()

    await expect(useCase.execute(driverUuid, lastSeededDate))
      .rejects.toThrow(expectedError)
  })

  it('Should insert 53 shifts if default shift contains only mondays and last seeded day is 31/12/2023', async () => {
    const repo = createStubInstance(SeedDriverShiftsRepository)
    repo.getDriver.resolves(
      new DriverEntityBuilder()
        .withDefaultShift(
          new DriverDefaultShiftEntityBuilder()
            .withDaysOfWeek([1])
            .build()
        )
        .build()
    )

    repo.createShift.callsFake((driver, date, defaultShift) => {
      return new ShiftEntityBuilder()
        .withlicensePlate(defaultShift.licensePlate)
        .withFrom(combineWithTime(date, defaultShift.from, defaultShift.timezone))
        .withUntil(combineWithTime(date, defaultShift.until, defaultShift.timezone))
        .withStartLocationUuid(defaultShift.startLocationUuid)
        .withStopLocationUuid(defaultShift.stopLocationUuid)
        .withDriverUuid(driver.uuid)
        .withBranchUuid(driver.branchUuid)
        .withCapacity(defaultShift.capacity)
        .withMaxDuration(DEFAULT_MAX_SHIFT_DURATION)
        .build()
    })

    const useCase = new SeedDriverShiftsUseCase(repo)
    const driverUuid = generateDriverUuid()
    const lastSeededDate = dayjs('2023-12-31')

    await useCase.execute(driverUuid, lastSeededDate)

    expect(repo.insertShifts.getCall(0).firstArg).toHaveLength(53)
  })
})
