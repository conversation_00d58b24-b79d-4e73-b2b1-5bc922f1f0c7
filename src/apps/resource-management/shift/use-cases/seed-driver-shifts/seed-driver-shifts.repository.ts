import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import dayjs from 'dayjs'
import { Shift } from '../../entities/shift.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { combineWithTime } from '../../../../../utils/dates/combine-dayjs-with-time.helper.js'
import { DriverDefaultShift } from '../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { DEFAULT_MAX_SHIFT_DURATION } from '../../constants.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'

export class SeedDriverShiftsRepository {
  constructor (
    @InjectRepository(Driver) private readonly driverRepository: Repository<Driver>,
    @InjectRepository(Shift) private readonly shiftRepository: Repository<Shift>
  ) {}

  async getDriver (driverUuid: DriverUuid): Promise<Driver> {
    return this.driverRepository.findOneOrFail({
      where: { uuid: driverUuid },
      relations: { defaultShift: true }
    })
  }

  createShift (
    driver: Driver,
    date: dayjs.Dayjs,
    defaultShift: DriverDefaultShift
  ): Shift {
    return this.shiftRepository.create({
      licensePlate: defaultShift.licensePlate,
      from: combineWithTime(date, defaultShift.from, defaultShift.timezone),
      until: combineWithTime(date, defaultShift.until, defaultShift.timezone),
      startLocationUuid: defaultShift.startLocationUuid,
      stopLocationUuid: defaultShift.stopLocationUuid,
      driverUuid: driver.uuid,
      branchUuid: driver.branchUuid,
      capacity: defaultShift.capacity,
      maxDuration: DEFAULT_MAX_SHIFT_DURATION // TODO move duration to command
    })
  }

  async insertShifts (shifts: Shift[]): Promise<void> {
    await this.shiftRepository.insert(shifts)
  }
}
