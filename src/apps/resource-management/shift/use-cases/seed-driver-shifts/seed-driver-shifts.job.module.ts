import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Shift } from '../../entities/shift.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { SeedDriverShiftsUseCase } from './seed-driver-shifts.use-case.js'
import { SeedDriverShiftsRepository } from './seed-driver-shifts.repository.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Driver,
      Shift
    ])
  ],
  providers: [
    SeedDriverShiftsUseCase,
    SeedDriverShiftsRepository
  ]
})
export class SeedDriverShiftsJobModule {}
