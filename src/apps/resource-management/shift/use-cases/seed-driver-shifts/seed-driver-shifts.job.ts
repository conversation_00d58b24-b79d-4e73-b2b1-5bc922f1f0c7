import { BaseJob, BaseJobData, PgBossJob } from '@wisemen/pgboss-nestjs-job'
import { QueueName } from '../../../../../modules/pgboss/enums/queue-name.enum.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'

export interface SeedDriverShiftsJobData extends BaseJobData {
  driverUuid: DriverUuid
  lastSeededDate: string
}

@PgBossJob(QueueName.SYSTEM)
export class SeedDriverShiftsJob extends BaseJob<SeedDriverShiftsJobData> {
  constructor (driverUuid: DriverUuid, lastSeededDate: Date) {
    super({
      driverUuid,
      lastSeededDate: lastSeededDate.toISOString()
    })
  }

  uniqueBy (data: SeedDriverShiftsJobData): string {
    return `process-driver-created-${data.driverUuid}`
  }
}
