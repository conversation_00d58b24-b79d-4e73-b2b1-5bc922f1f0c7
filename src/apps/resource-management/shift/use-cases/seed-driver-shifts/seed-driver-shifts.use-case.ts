import { Injectable } from '@nestjs/common'
import dayjs from 'dayjs'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { Shift } from '../../entities/shift.entity.js'
import { DriverDoesNotHaveDefaultShift } from '../../errors/driver-does-not-have-default-shift.error.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'
import { SeedDriverShiftsRepository } from './seed-driver-shifts.repository.js'

@Injectable()
export class SeedDriverShiftsUseCase {
  constructor (
    private readonly repository: SeedDriverShiftsRepository
  ) {}

  async execute (driverUuid: DriverUuid, lastSeededDate: dayjs.Dayjs): Promise<void> {
    const driver = await this.repository.getDriver(driverUuid)
    await this.seedDriverShifts(driver, lastSeededDate)
  }

  private async seedDriverShifts (driver: Driver, lastSeededDate: dayjs.Dayjs): Promise<void> {
    if (driver.defaultShift == null) {
      throw new DriverDoesNotHaveDefaultShift()
    }

    const defaultShift = driver.defaultShift

    const begin = lastSeededDate.add(1, 'day')
    const endOfYear = begin.endOf('year')

    const shifts: Shift[] = []
    for (let date = begin; date.isBefore(endOfYear); date = date.add(1, 'day')) {
      const dayOfWeek = date.isoWeekday()

      if (!defaultShift.daysOfWeek.includes(dayOfWeek)) {
        continue
      }

      shifts.push(this.repository.createShift(driver, date, defaultShift))
    }

    await this.repository.insertShifts(shifts)
  }
}
