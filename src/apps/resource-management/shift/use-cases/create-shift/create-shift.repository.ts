import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Any, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Repository } from 'typeorm'
import { Shift } from '../../entities/shift.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { Location } from '../../../../../modules/location/entities/location.entity.js'
import {
  LocationNotFoundError
} from '../../../driver-default-shift/errors/location-not-found.error.js'
import { Absence } from '../../../absence/entities/absence.entity.js'
import { FullDayAbsence } from '../../../absence/entities/full-day-absence.entity.js'
import { DriverNotFoundError } from '../../../driver/errors/driver-not-found.error.js'
import { LocationUuid } from '../../../../../modules/location/entities/location.uuid.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'
import { CreateShiftDriver } from './create-shift-driver.js'

@Injectable()
export class CreateShiftRepository {
  constructor (
    @InjectRepository(Shift) private readonly shiftRepo: Repository<Shift>,
    @InjectRepository(Driver) private readonly driverRepo: Repository<Driver>,
    @InjectRepository(Location) private readonly locationRepo: Repository<Location>,
    @InjectRepository(Absence) private readonly absenceRepo: Repository<Absence>,
    @InjectRepository(FullDayAbsence)
    private readonly fullDayAbsenceRepo: Repository<FullDayAbsence>
  ) {}

  async findDriverOrFail (driverUuid: DriverUuid): Promise<CreateShiftDriver> {
    const driver = await this.driverRepo.findOne({
      where: { uuid: driverUuid },
      relations: { branch: true, defaultShift: true }
    })
    assert(driver !== null, new DriverNotFoundError(driverUuid))
    return driver as CreateShiftDriver
  }

  async findLocationOrFail (
    startLocationUuid: LocationUuid,
    stopLocationUuid: LocationUuid
  ): Promise<[Location, Location]> {
    const locations = await this.locationRepo.findBy({
      uuid: Any([startLocationUuid, stopLocationUuid])
    })
    const startLocation = locations.find(location => location.uuid === startLocationUuid)
    const stopLocation = locations.find(location => location.uuid === startLocationUuid)
    assert(startLocation != null, new LocationNotFoundError())
    assert(stopLocation != null, new LocationNotFoundError())
    return [startLocation, stopLocation]
  }

  async insertShift (shift: Shift): Promise<void> {
    await this.shiftRepo.insert(shift)
  }

  async overlappingShiftExists (shift: Shift): Promise<boolean> {
    return this.shiftRepo.existsBy({
      driverUuid: shift.driverUuid,
      from: LessThan(shift.until),
      until: MoreThan(shift.from)
    })
  }

  async overlappingAbsenceExists (shift: Shift): Promise<boolean> {
    return this.absenceRepo.existsBy({
      driverUuid: shift.driverUuid,
      from: LessThan(shift.until),
      until: MoreThan(shift.from)
    })
  }

  async fullDayAbsenceExists (forDriverUuid: string, date: string): Promise<boolean> {
    return this.fullDayAbsenceRepo.existsBy({
      uuid: forDriverUuid,
      date
    })
  }
}
