import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Shift } from '../../entities/shift.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { Location } from '../../../../../modules/location/entities/location.entity.js'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { Absence } from '../../../absence/entities/absence.entity.js'
import { FullDayAbsence } from '../../../absence/entities/full-day-absence.entity.js'
import { CreateShiftController } from './create-shift.controller.js'
import { CreateShiftUseCase } from './create-shift.use-case.js'
import { CreateShiftRepository } from './create-shift.repository.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Shift,
      Driver,
      Location,
      Absence,
      FullDayAbsence
    ]),
    DomainEventEmitterModule
  ],
  providers: [
    CreateShiftUseCase,
    CreateShiftRepository
  ],
  controllers: [
    CreateShiftController
  ]
})
export class CreateShiftModule {}
