import { Injectable } from '@nestjs/common'
import dayjs from 'dayjs'
import { DataSource } from 'typeorm'
import { transaction } from '@wisemen/nestjs-typeorm'
import { Shift } from '../../entities/shift.entity.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import {
  ShiftOverlapsWithExistingShiftsError
} from '../../errors/shift-overlaps-with-existing-shifts.error.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { ShiftOverlapsWithAbsenceError } from '../../errors/shift-overlaps-with-absence.error.js'
import { DEFAULT_MAX_SHIFT_DURATION } from '../../constants.js'
import { DateFormats } from '../../../../../utils/dates/date-formats.js'
import {
  ShiftOverlapsWithFullDayAbsenceError
} from '../../errors/shift-overlaps-with-full-day-absence.error.js'
import { CreateShiftResponse } from './create-shift.response.js'
import { CreateShiftCommand } from './create-shift.command.js'
import { ShiftCreatedEvent } from './shift-created.event.js'
import { CreateShiftRepository } from './create-shift.repository.js'

@Injectable()
export class CreateShiftUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    private readonly repository: CreateShiftRepository
  ) {}

  async execute (command: CreateShiftCommand): Promise<CreateShiftResponse> {
    const driver = await this.repository.findDriverOrFail(command.driverUuid)
    const shift = this.createShift(driver, command)
    const shiftDate = dayjs(shift.from).format(DateFormats.DATE)

    if (await this.repository.fullDayAbsenceExists(command.driverUuid, shiftDate)) {
      throw new ShiftOverlapsWithFullDayAbsenceError()
    }

    if (await this.repository.overlappingShiftExists(shift)) {
      throw new ShiftOverlapsWithExistingShiftsError()
    }

    if (await this.repository.overlappingAbsenceExists(shift)) {
      throw new ShiftOverlapsWithAbsenceError()
    }

    await transaction(this.dataSource, async () => {
      await this.repository.insertShift(shift)
      await this.eventEmitter.emitOne(new ShiftCreatedEvent(shift.uuid))
      return shift
    })

    shift.driver = driver
    shift.branch = driver.branch

    return new CreateShiftResponse(shift)
  }

  createShift (driver: Driver, command: CreateShiftCommand): Shift {
    const shift = new Shift()
    shift.driverUuid = driver.uuid
    shift.from = dayjs(command.from).toDate()
    shift.until = dayjs(command.until).toDate()
    shift.startLocationUuid = driver.defaultShift.startLocationUuid
    shift.stopLocationUuid = driver.defaultShift.stopLocationUuid
    shift.branchUuid = driver.branchUuid
    shift.licensePlate = driver.defaultShift.licensePlate
    shift.maxDuration = DEFAULT_MAX_SHIFT_DURATION
    shift.capacity = driver.defaultShift.capacity
    return shift
  }
}
