import { before, beforeEach, describe, it } from 'node:test'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { CreateShiftUseCase } from '../create-shift.use-case.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { DriverEntityBuilder } from '../../../../driver/entities/driver/driver.entity.builder.js'
import { ShiftCreatedEvent } from '../shift-created.event.js'
import { CreateShiftRepository } from '../create-shift.repository.js'
import { ShiftOverlapsWithExistingShiftsError } from '../../../errors/shift-overlaps-with-existing-shifts.error.js'
import { BranchEntityBuilder } from '../../../../branch/builders/branch.entity.builder.js'
import { ShiftOverlapsWithAbsenceError } from '../../../errors/shift-overlaps-with-absence.error.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { CreateShiftDriver } from '../create-shift-driver.js'
import {
  ShiftOverlapsWithFullDayAbsenceError
} from '../../../errors/shift-overlaps-with-full-day-absence.error.js'
import { DriverNotFoundError } from '../../../../driver/errors/driver-not-found.error.js'
import { CreateShiftCommandBuilder } from './create-shift-command.builder.js'

describe('UpdateShiftUseCase - Unit test', () => {
  let createShiftRepo: SinonStubbedInstance<CreateShiftRepository>
  before(() => {
    TestBench.setupUnitTest()
  })

  beforeEach(() => {
    const branch = new BranchEntityBuilder().build()
    const defaultShift = new DriverDefaultShiftEntityBuilder().build()
    const driver = new DriverEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withDefaultShiftUuid(defaultShift.uuid)
      .build()

    driver.branch = branch
    driver.defaultShift = defaultShift

    createShiftRepo = createStubInstance(CreateShiftRepository)
    createShiftRepo.findDriverOrFail.resolves(driver as CreateShiftDriver)
    createShiftRepo.findLocationOrFail.resolves([
      new LocationBuilder().build(),
      new LocationBuilder().build()
    ])
    createShiftRepo.overlappingShiftExists.resolves(false)
    createShiftRepo.overlappingAbsenceExists.resolves(false)
    createShiftRepo.fullDayAbsenceExists.resolves(false)
  })

  it('Throws a DriverNotFoundError when the driver does not exist', async () => {
    createShiftRepo.findDriverOrFail.rejects(new DriverNotFoundError())

    const useCase = new CreateShiftUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      createShiftRepo
    )

    const command = new CreateShiftCommandBuilder().build()
    await expect(useCase.execute(command)).rejects.toThrow(DriverNotFoundError)
  })

  it('Throws an error when the new shift overlaps with existing shifts', async () => {
    createShiftRepo.overlappingShiftExists.resolves(true)

    const useCase = new CreateShiftUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      createShiftRepo
    )

    const command = new CreateShiftCommandBuilder().build()
    await expect(useCase.execute(command))
      .rejects.toThrow(ShiftOverlapsWithExistingShiftsError)
  })

  it('Throws an error when the new shift overlaps with a full day absence', async () => {
    createShiftRepo.fullDayAbsenceExists.resolves(true)

    const useCase = new CreateShiftUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      createShiftRepo
    )

    const command = new CreateShiftCommandBuilder().build()
    await expect(useCase.execute(command))
      .rejects.toThrow(ShiftOverlapsWithFullDayAbsenceError)
  })

  it('Throws an error when the new shift overlaps with existing absences', async () => {
    createShiftRepo.overlappingAbsenceExists.resolves(true)

    const useCase = new CreateShiftUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      createShiftRepo
    )

    const command = new CreateShiftCommandBuilder().build()
    await expect(useCase.execute(command))
      .rejects.toThrow(ShiftOverlapsWithAbsenceError)
  })

  it('emits a ShiftCreatedEvent', async () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)

    const useCase = new CreateShiftUseCase(
      stubDataSource(),
      eventEmitter,
      createShiftRepo
    )

    const command = new CreateShiftCommandBuilder().build()
    const shift = await useCase.execute(command)

    expect(eventEmitter).toHaveEmitted(new ShiftCreatedEvent(shift.uuid))
  })
})
