import { Dayjs } from 'dayjs'
import { CreateShiftCommand } from '../create-shift.command.js'
import { DateFormats } from '../../../../../../utils/dates/date-formats.js'
import { DriverUuid, generateDriverUuid } from '../../../../driver/entities/driver/driver.uuid.js'

export class CreateShiftCommandBuilder {
  private command: CreateShiftCommand
  constructor () {
    this.command = new CreateShiftCommand()
    this.command.driverUuid = generateDriverUuid()
    this.command.until = new Date().toISOString()
    this.command.from = new Date().toISOString()
  }

  withFrom (date: Dayjs): this {
    this.command.from = date.format(DateFormats.ISO_8601)
    return this
  }

  withUntil (date: Dayjs): this {
    this.command.until = date.format(DateFormats.ISO_8601)
    return this
  }

  withDriverUuid (uuid: DriverUuid): this {
    this.command.driverUuid = uuid
    return this
  }

  build (): CreateShiftCommand {
    return this.command
  }
}
