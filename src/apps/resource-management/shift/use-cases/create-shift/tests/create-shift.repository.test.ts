import { after, before, describe, it } from 'node:test'
import { TypeOrmRepository } from '@wisemen/nestjs-typeorm'
import { expect } from 'expect'
import { Shift } from '../../../entities/shift.entity.js'
import { Driver } from '../../../../driver/entities/driver/driver.entity.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { Branch } from '../../../../branch/branch.entity.js'
import { CreateShiftRepository } from '../create-shift.repository.js'
import { DriverEntityBuilder } from '../../../../driver/entities/driver/driver.entity.builder.js'
import { BranchEntityBuilder } from '../../../../branch/builders/branch.entity.builder.js'
import { ShiftEntityBuilder } from '../../../builders/shift.entity.builder.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { Absence } from '../../../../absence/entities/absence.entity.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../driver-default-shift/entities/driver-default-shift.entity.js'
import { FullDayAbsence } from '../../../../absence/entities/full-day-absence.entity.js'
import { RepositoryTestSetup } from '../../../../../../../test/setup/repository-test-setup.js'

describe('UpdateShiftRepository tests', () => {
  let setup: RepositoryTestSetup
  let repository: CreateShiftRepository
  let branch: Branch
  let location: Location

  before(async () => {
    setup = await TestBench.setupRepositoryTest()
    repository = new CreateShiftRepository(
      new TypeOrmRepository(Shift, setup.dataSource.manager),
      new TypeOrmRepository(Driver, setup.dataSource.manager),
      new TypeOrmRepository(Location, setup.dataSource.manager),
      new TypeOrmRepository(Absence, setup.dataSource.manager),
      new TypeOrmRepository(FullDayAbsence, setup.dataSource.manager)
    )
    branch = new BranchEntityBuilder().build()
    await setup.dataSource.manager.insert(Branch, branch)
    location = new LocationBuilder().build()
    await setup.dataSource.manager.insert(Location, location)
  })

  after(async () => await setup.teardown())

  describe('Finds overlapping shifts', () => {
    it('Finds a shift which envelops the new shift', async () => {
      const location = new LocationBuilder().build()
      await setup.dataSource.manager.insert(Location, location)
      const defaultShift = new DriverDefaultShiftEntityBuilder()
        .withStartLocationUuid(location.uuid)
        .withStopLocationUuid(location.uuid)
        .build()

      await setup.dataSource.manager.insert(DriverDefaultShift, defaultShift)

      const driver = new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withDefaultShiftUuid(defaultShift.uuid)
        .build()
      await setup.dataSource.manager.insert(Driver, driver)

      const existingShift = new ShiftEntityBuilder()
        .withDriverUuid(driver.uuid)
        .withBranchUuid(branch.uuid)
        .withStartLocationUuid(location.uuid)
        .withStopLocationUuid(location.uuid)
        .withFrom(new Date('2024-01-01 10:00:00'))
        .withUntil(new Date('2024-01-01 20:00:00'))
        .build()

      await setup.dataSource.manager.insert(Shift, existingShift)

      const newShift = new ShiftEntityBuilder()
        .withDriverUuid(driver.uuid)
        .withFrom(new Date('2024-01-01 10:00:01'))
        .withUntil(new Date('2024-01-01 19:59:59'))
        .build()

      const overlaps = await repository.overlappingShiftExists(newShift)
      expect(overlaps).toBe(true)
    })

    it('Finds a shift which is enveloped by the new shift', async () => {
      const location = new LocationBuilder().build()
      await setup.dataSource.manager.insert(Location, location)

      const defaultShift = new DriverDefaultShiftEntityBuilder()
        .withStartLocationUuid(location.uuid)
        .withStopLocationUuid(location.uuid)
        .build()
      await setup.dataSource.manager.insert(DriverDefaultShift, defaultShift)

      const driver = new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withDefaultShiftUuid(defaultShift.uuid)
        .build()
      await setup.dataSource.manager.insert(Driver, driver)

      const existingShift = new ShiftEntityBuilder()
        .withDriverUuid(driver.uuid)
        .withBranchUuid(branch.uuid)
        .withStartLocationUuid(location.uuid)
        .withStopLocationUuid(location.uuid)
        .withFrom(new Date('2024-01-01 10:00:00'))
        .withUntil(new Date('2024-01-01 20:00:00'))
        .build()

      await setup.dataSource.manager.insert(Shift, existingShift)

      const newShift = new ShiftEntityBuilder()
        .withDriverUuid(driver.uuid)
        .withFrom(new Date('2024-01-01 09:59:59'))
        .withUntil(new Date('2024-01-01 20:00:01'))
        .build()

      const overlaps = await repository.overlappingShiftExists(newShift)
      expect(overlaps).toBe(true)
    })

    it('Finds a shift which partially overlaps with the new shift', async () => {
      const location = new LocationBuilder().build()
      await setup.dataSource.manager.insert(Location, location)

      const defaultShift = new DriverDefaultShiftEntityBuilder()
        .withStartLocationUuid(location.uuid)
        .withStopLocationUuid(location.uuid)
        .build()
      await setup.dataSource.manager.insert(DriverDefaultShift, defaultShift)

      const driver = new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withDefaultShiftUuid(defaultShift.uuid)
        .build()
      await setup.dataSource.manager.insert(Driver, driver)

      const existingShift = new ShiftEntityBuilder()
        .withDriverUuid(driver.uuid)
        .withBranchUuid(branch.uuid)
        .withStartLocationUuid(location.uuid)
        .withStopLocationUuid(location.uuid)
        .withFrom(new Date('2024-01-01 10:00:00'))
        .withUntil(new Date('2024-01-01 20:00:00'))
        .build()

      await setup.dataSource.manager.insert(Shift, existingShift)

      const newShift = new ShiftEntityBuilder()
        .withDriverUuid(driver.uuid)
        .withFrom(new Date('2024-01-01 19:00:00'))
        .withUntil(new Date('2024-01-01 21:00:00'))
        .build()

      const overlaps = await repository.overlappingShiftExists(newShift)
      expect(overlaps).toBe(true)
    })

    it('Does not find a shifts before and after the new shift', async () => {
      const location = new LocationBuilder().build()
      await setup.dataSource.manager.insert(Location, location)

      const defaultShift = new DriverDefaultShiftEntityBuilder()
        .withStartLocationUuid(location.uuid)
        .withStopLocationUuid(location.uuid)
        .build()
      await setup.dataSource.manager.insert(DriverDefaultShift, defaultShift)

      const driver = new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withDefaultShiftUuid(defaultShift.uuid)
        .build()
      await setup.dataSource.manager.insert(Driver, driver)

      const shiftBefore = new ShiftEntityBuilder()
        .withDriverUuid(driver.uuid)
        .withBranchUuid(branch.uuid)
        .withStartLocationUuid(location.uuid)
        .withStopLocationUuid(location.uuid)
        .withFrom(new Date('2024-01-01 10:00:00'))
        .withUntil(new Date('2024-01-01 20:00:00'))
        .build()

      const shiftAfter = new ShiftEntityBuilder()
        .withDriverUuid(driver.uuid)
        .withBranchUuid(branch.uuid)
        .withStartLocationUuid(location.uuid)
        .withStopLocationUuid(location.uuid)
        .withFrom(new Date('2024-01-01 21:00:00'))
        .withUntil(new Date('2024-01-01 22:00:00'))
        .build()

      await setup.dataSource.manager.insert(Shift, [shiftBefore, shiftAfter])

      const newShift = new ShiftEntityBuilder()
        .withDriverUuid(driver.uuid)
        .withFrom(new Date('2024-01-01 20:00:01'))
        .withUntil(new Date('2024-01-01 20:59:59'))
        .build()

      const overlaps = await repository.overlappingShiftExists(newShift)
      expect(overlaps).toBe(false)
    })
  })
})
