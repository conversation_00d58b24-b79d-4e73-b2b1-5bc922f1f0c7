import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import type { DataSource } from 'typeorm'
import { NestExpressApplication } from '@nestjs/platform-express'
import dayjs from 'dayjs'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { Shift } from '../../../entities/shift.entity.js'
import { Driver } from '../../../../driver/entities/driver/driver.entity.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { CreateShiftTestContext, setupCreateShiftContext } from './create-shift.test-context.js'
import { CreateShiftCommandBuilder } from './create-shift-command.builder.js'

describe('CreateShift - E2e test', () => {
  let testSetup: EndToEndTestSetup
  let app: NestExpressApplication
  let dataSource: DataSource
  let adminUser: TestUser
  let driver: Driver

  let testContext: CreateShiftTestContext
  let existingShift: Shift

  before(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    dataSource = testSetup.dataSource
    app = testSetup.app

    adminUser = await testSetup.authContext.getAdminUser()

    testContext = await setupCreateShiftContext(dataSource.manager)
    existingShift = testContext.existingShift
    driver = testContext.driver
  })

  after(async () => {
    await testSetup.teardown()
  })

  it('creates a shift', async () => {
    const newFromValue = dayjs(existingShift.from).add(1, 'day')
    const newUntilValue = dayjs(existingShift.until).add(1, 'day')
    const command = new CreateShiftCommandBuilder()
      .withFrom(newFromValue)
      .withUntil(newUntilValue)
      .withDriverUuid(driver.uuid)
      .build()

    const response = await request(app.getHttpServer())
      .post(`/shifts`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command)

    expect(response).toHaveStatus(201)
    expect(response.body).toStrictEqual(expect.objectContaining({
      uuid: expect.uuid()
    }))
  })
})
