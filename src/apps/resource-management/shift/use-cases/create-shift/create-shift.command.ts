import { ApiProperty } from '@nestjs/swagger'
import { IsDateString, IsNotEmpty, IsUUID } from 'class-validator'
import { DateFormats } from '../../../../../utils/dates/date-formats.js'
import { IsSameOrBeforeDateString } from '../../../../../utils/validators/is-same-or-before-date.js'
import { DriverUuid } from '../../../driver/entities/driver/driver.uuid.js'

export class CreateShiftCommand {
  @ApiProperty({ type: 'string', format: 'uuid' })
  @IsUUID()
  @IsNotEmpty()
  driverUuid: DriverUuid

  @IsDateString({ strict: true })
  @IsSameOrBeforeDateString({
    dateProvider: (obj: CreateShiftCommand) => obj.until,
    format: DateFormats.ISO_8601,
    granularity: 'minutes'
  })
  @ApiProperty({
    description: 'The start time of the shift in ISO 8601 format (UTC)',
    example: DateFormats.ISO_8601,
    type: 'string',
    format: 'date-time'
  })
  from: string

  @IsDateString({ strict: true })
  @ApiProperty({
    description: 'The end time of the shift',
    example: DateFormats.ISO_8601,
    type: 'string',
    format: 'date-time'
  })
  until: string
}
