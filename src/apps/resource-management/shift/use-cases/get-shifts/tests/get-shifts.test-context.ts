import { <PERSON><PERSON><PERSON><PERSON>anager } from 'typeorm'
import dayjs from 'dayjs'
import { Branch } from '../../../../branch/branch.entity.js'
import { BranchEntityBuilder } from '../../../../branch/builders/branch.entity.builder.js'
import { DriverEntityBuilder } from '../../../../driver/entities/driver/driver.entity.builder.js'
import { Driver } from '../../../../driver/entities/driver/driver.entity.js'
import { ShiftEntityBuilder } from '../../../builders/shift.entity.builder.js'
import { Shift } from '../../../entities/shift.entity.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { AbsenceEntityBuilder } from '../../../../absence/builders/absence.entity.builder.js'
import { Absence } from '../../../../absence/entities/absence.entity.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../driver-default-shift/entities/driver-default-shift.entity.js'

export interface GetShiftsTestContext {
  existingShift: Shift
  existingAbsence: Absence
  location: Location
  driver: Driver
  branch: Branch
}

export async function setupShiftIndexContext (
  entityManager: EntityManager
): Promise<GetShiftsTestContext> {
  const branch = new BranchEntityBuilder().build()
  const location = new LocationBuilder().build()

  await Promise.all([
    entityManager.insert(Branch, branch),
    entityManager.insert(Location, location)
  ])

  const defaultShift = new DriverDefaultShiftEntityBuilder()
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const driver = new DriverEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withDefaultShiftUuid(defaultShift.uuid)
    .build()

  await entityManager.insert(DriverDefaultShift, defaultShift)
  await entityManager.insert(Driver, driver)

  const existingAbsence = new AbsenceEntityBuilder()
    .withDriverUuid(driver.uuid)
    .build()
  await entityManager.insert(Absence, existingAbsence)

  const notFindableAbsence = new AbsenceEntityBuilder()
    .withDriverUuid(driver.uuid)
    .withFrom(dayjs('2024-01-01').startOf('day').toDate())
    .withUntil(dayjs('2024-01-01').endOf('day').toDate())
    .build()
  await entityManager.insert(Absence, notFindableAbsence)

  const existingShift = new ShiftEntityBuilder()
    .withDriverUuid(driver.uuid)
    .withBranchUuid(branch.uuid)
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()
  await entityManager.insert(Shift, existingShift)

  const notFindableShift = new ShiftEntityBuilder()
    .withDriverUuid(driver.uuid)
    .withBranchUuid(branch.uuid)
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .withFrom(dayjs('2024-01-01').toDate())
    .withUntil(dayjs('2024-01-01').toDate())
    .build()
  await entityManager.insert(Shift, notFindableShift)

  return {
    existingShift,
    existingAbsence,
    location: location,
    driver,
    branch
  }
}
