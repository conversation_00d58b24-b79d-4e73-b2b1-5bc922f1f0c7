import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import type { DataSource } from 'typeorm'
import { NestExpressApplication } from '@nestjs/platform-express'
import dayjs from 'dayjs'
import { Shift } from '../../../entities/shift.entity.js'
import { Driver } from '../../../../driver/entities/driver/driver.entity.js'
import { DateFormats } from '../../../../../../utils/dates/date-formats.js'
import { Absence } from '../../../../absence/entities/absence.entity.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { GetShiftsTestContext, setupShiftIndexContext } from './get-shifts.test-context.js'

describe('Get Shifts - E2e test', () => {
  let testSetup: EndToEndTestSetup
  let app: NestExpressApplication
  let dataSource: DataSource
  let adminUser: TestUser
  let driver: Driver

  let testContext: GetShiftsTestContext
  let existingShift: Shift
  let existingAbsence: Absence

  before(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    dataSource = testSetup.dataSource
    app = testSetup.app

    adminUser = await testSetup.authContext.getAdminUser()

    testContext = await setupShiftIndexContext(dataSource.manager)
    existingShift = testContext.existingShift
    existingAbsence = testContext.existingAbsence
    driver = testContext.driver
  })

  after(async () => {
    await testSetup.teardown()
  })

  it('Get index of a shift', async () => {
    const response = await request(app.getHttpServer())
      .get(`/shifts`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query({
        filter: {
          date: dayjs(existingShift.from).format(DateFormats.POSTGRES_DATE),
          driverUuid: driver.uuid
        }
      })

    expect(response).toHaveStatus(200)
    expect(response.body.shifts).toHaveLength(1)
    expect(response.body.shifts[0].from).toEqual(
      dayjs(existingShift.from).format(DateFormats.ISO_8601)
    )
    expect(response.body.shifts[0].until).toEqual(
      dayjs(existingShift.until).format(DateFormats.ISO_8601)
    )
    expect(response.body.shifts[0].licensePlate).toEqual(existingShift.licensePlate)
    expect(response.body.shifts[0].startLocation.uuid).toEqual(existingShift.startLocationUuid)
    expect(response.body.shifts[0].stopLocation.uuid).toEqual(existingShift.stopLocationUuid)
    expect(response.body.shifts[0].branch.uuid).toEqual(existingShift.branchUuid)

    expect(response.body.absences).toHaveLength(1)
    expect(response.body.absences[0].from).toEqual(dayjs(existingAbsence.from).toISOString())
    expect(response.body.absences[0].until).toEqual(dayjs(existingAbsence.until).toISOString())
    expect(response.body.absences[0].type).toEqual(existingAbsence.type)
  })
})
