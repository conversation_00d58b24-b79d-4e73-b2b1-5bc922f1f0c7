import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { typeormPagination } from '@wisemen/pagination'
import { Repository } from 'typeorm'
import { Vehicle } from '../../entities/vehicle.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { ViewVehicleIndexQuery } from './view-vehicle-index.query.js'
import { ViewVehicleIndexResponse } from './view-vehicle-index.response.js'

@Injectable()
export class ViewVehicleIndexUseCase {
  constructor (
    @InjectRepository(Vehicle)
    private vehicleRepository: Repository<Vehicle>,

    @InjectRepository(Driver)
    private driverRepository: Repository<Driver>
  ) {}

  public async execute (
    query: ViewVehicleIndexQuery
  ): Promise<ViewVehicleIndexResponse> {
    const pagination = typeormPagination(query.pagination)

    const queryBuilder = this.vehicleRepository.createQueryBuilder('vehicle')
      .leftJoinAndSelect('vehicle.startLocation', 'startLocation')
      .leftJoinAndSelect('vehicle.endLocation', 'endLocation')
      .leftJoinAndSelect('vehicle.depotLocation', 'depotLocation')

    if (query.filter?.isActive !== undefined) {
      if (query.filter.isActive === 'true') {
        queryBuilder.andWhere('vehicle.deletedAt IS NULL')
      } else {
        queryBuilder.andWhere('vehicle.deletedAt IS NOT NULL')
      }
    }

    if (query.filter?.licensePlate !== undefined) {
      queryBuilder.andWhere('vehicle.licensePlate LIKE :licensePlate', {
        licensePlate: `%${query.filter.licensePlate}%`
      })
    }

    if (query.filter?.startDate !== undefined) {
      queryBuilder.andWhere('vehicle.createdAt >= :startDate', {
        startDate: query.filter.startDate
      })
    }

    if (query.filter?.endDate !== undefined) {
      queryBuilder.andWhere('vehicle.createdAt <= :endDate', {
        endDate: query.filter.endDate
      })
    }

    if (query.filter?.capacity !== undefined) {
      queryBuilder.andWhere('vehicle.capacity = :capacity', {
        capacity: query.filter.capacity
      })
    }

    if (query.filter?.startLocationUuid !== undefined) {
      queryBuilder.andWhere('vehicle.startLocationUuid = :startLocationUuid', {
        startLocationUuid: query.filter.startLocationUuid
      })
    }

    if (query.filter?.endLocationUuid !== undefined) {
      queryBuilder.andWhere('vehicle.endLocationUuid = :endLocationUuid', {
        endLocationUuid: query.filter.endLocationUuid
      })
    }

    if (query.search !== undefined) {
      queryBuilder.andWhere('vehicle.licensePlate LIKE :search', {
        search: `%${query.search}%`
      })
    }

    const [items, totalCount] = await queryBuilder
      .orderBy('vehicle.licensePlate', 'ASC')
      .skip(pagination.skip)
      .take(pagination.take)
      .getManyAndCount()

    return new ViewVehicleIndexResponse(
      items,
      totalCount,
      pagination.take,
      pagination.skip
    )
  }
}
