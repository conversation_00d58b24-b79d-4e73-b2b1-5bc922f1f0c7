import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Vehicle } from '../../entities/vehicle.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { ViewVehicleIndexUseCase } from './view-vehicle-index.use-case.js'
import { ViewVehicleIndexController } from './view-vehicle-index.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([Vehicle, Driver])
  ],
  controllers: [
    ViewVehicleIndexController
  ],
  providers: [
    ViewVehicleIndexUseCase
  ]
})
export class ViewVehicleIndexModule { }
