import { Module } from '@nestjs/common'
import { DomainEventLogSubscriberModule } from '../domain-event-log/use-cases/log-event/domain-event-log-subscriber.module.js'
import { AssignDefaultRoleToUserSubscriberModule } from '../../app/users/use-cases/assign-default-role-to-user/assign-default-role-to-user-subscriber.module.js'
import { ClearRolePermissionsCacheSubscriberModule } from '../../app/roles/use-cases/clear-role-permissions-cache/clear-role-permissions-cache-subscriber.module.js'
import { UserTypesenseSubscriberModule } from '../../app/users/typesense/user-typesense.subscriber.module.js'
import { CreateUserNotificationsSubscriberModule } from '../notification/use-cases/create-user-notifications/create-user-notifications.subscriber-module.js'
import { SendAppNotificationSubscriberModule } from '../notification/use-cases/send-app-notification/send-app-notification.subscriber.module.js'
import { AssignDefaultNotificationPreferencesToUserSubscriberModule } from '../notification/use-cases/assign-default-notification-preferences-to-user/assign-default-notification-preferences-to-user.subscriber.module.js'
import { CareUserTypesenseSubscriberModule } from '../../apps/order-management/care-user/typesense/care-user-typesense.subscriber.module.js'
import { ClientTypesenseSubscriberModule } from '../../apps/order-management/client/typesense/client-typesense.subscriber.module.js'
import { ContractTypeTypesenseSubscriberModule } from '../../apps/order-management/contract-type/typesense/contract-type-typesense.subscriber.module.js'
import { MaxTimeInVehicleFormulaTypesenseSubscriberModule } from '../../apps/order-management/max-time-in-vehicle-formula/typesense/max-time-in-vehicle-formula-typesense.subscriber.module.js'
import { OrganizationTypesenseSubscriberModule } from '../../apps/order-management/organization/typesense/organization.typesense-subscriber.module.js'
import { BranchTypesenseSubscriberModule } from '../../apps/resource-management/branch/typesense/branch-typesense.subscriber.module.js'
import { DriverTypesenseSubscriberModule } from '../../apps/resource-management/driver/typesense/driver-typesense.subscriber.module.js'
import { LocationTypesenseSubscriberModule } from '../location/typesense/location-typesense.subscriber.module.js'
import { SimulationCreatedNotificationSubscriberModule } from '../../apps/planning/simulation/notifications/simulation-created-notification/simulation-created-notification.subscriber.module.js'
import { SimulationFailedNotificationSubscriberModule } from '../../apps/planning/simulation/notifications/simulation-failed-notification/simulation-failed-notification.subscriber.module.js'
import { ProcessAtoChangesSubscriberModule } from '../../apps/planning/simulation/use-cases/process-ato-changes/process-ato-changes.subscriber.module.js'
import { DriverCreatedNotificationSubscriberModule } from '../../apps/resource-management/driver/notifications/driver-created-notification/driver-created-notification-subscriber.module.js'
import { HideShiftsSubscriberModule } from '../../apps/resource-management/shift/use-cases/hide-shift/hide-shifts.subscriber.module.js'
import { RevealShiftsSubscriberModule } from '../../apps/resource-management/shift/use-cases/reveal-shifts/reveal-shifts.subscriber.module.js'
import { SeedDriverShiftsSubscriberModule } from '../../apps/resource-management/shift/use-cases/seed-driver-shifts/seed-driver-shifts.subscriber.module.js'
import { SyncHolidaysLookupSubscriberModule } from '../holidays/subscribers/sync-holidays-lookup-subscriber.module.js'
import { VehicleTypesenseSubscriberModule } from '../../apps/resource-management/vehicle/typesense/vehicle-typesense.subscriber.module.js'
import { AssignDefaultContractToClientSubscriberModule } from '../../apps/order-management/client/use-cases/assign-contract-on-create/assign-default-contract-to-client.subscriber.module.js'

@Module({
  imports: [
    DomainEventLogSubscriberModule,
    AssignDefaultRoleToUserSubscriberModule,
    ClearRolePermissionsCacheSubscriberModule,
    UserTypesenseSubscriberModule,
    CreateUserNotificationsSubscriberModule,
    SendAppNotificationSubscriberModule,
    AssignDefaultNotificationPreferencesToUserSubscriberModule,
    SendAppNotificationSubscriberModule,
    LocationTypesenseSubscriberModule,
    DriverTypesenseSubscriberModule,
    BranchTypesenseSubscriberModule,
    OrganizationTypesenseSubscriberModule,
    MaxTimeInVehicleFormulaTypesenseSubscriberModule,
    ContractTypeTypesenseSubscriberModule,
    ClientTypesenseSubscriberModule,
    CareUserTypesenseSubscriberModule,
    ProcessAtoChangesSubscriberModule,
    HideShiftsSubscriberModule,
    RevealShiftsSubscriberModule,
    SeedDriverShiftsSubscriberModule,
    SimulationCreatedNotificationSubscriberModule,
    SimulationFailedNotificationSubscriberModule,
    DriverCreatedNotificationSubscriberModule,
    SyncHolidaysLookupSubscriberModule,
    VehicleTypesenseSubscriberModule,
    AssignDefaultContractToClientSubscriberModule
  ]
})
export class DomainEventSubscribersModule {}
