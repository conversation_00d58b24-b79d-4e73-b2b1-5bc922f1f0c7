import fs from 'fs'
import { Injectable } from '@nestjs/common'
import dayjs from 'dayjs'
import { Time } from '@wisemen/time'
import { Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { csvStringToObjectArray } from '../../utils/csv/parse-csv.js'
import { Driver } from '../../apps/resource-management/driver/entities/driver/driver.entity.js'
import { Branch } from '../../apps/resource-management/branch/branch.entity.js'
import {
  DriverDefaultShift
} from '../../apps/resource-management/driver-default-shift/entities/driver-default-shift.entity.js'
import { Timezone } from '../../utils/dates/timezone.enum.js'
import { generateUuid } from './uuid-generator.js'
import { RawShiftCsvRecord } from './records/raw-shift-csv.record.js'

@Injectable()
export class SeedDriversAndDefaultShiftsUseCase {
  constructor (
    @InjectRepository(Branch) private readonly branchRepository: Repository<Branch>,
    @InjectRepository(Driver) private readonly driverRepository: Repository<Driver>,
    @InjectRepository(DriverDefaultShift)
    private readonly defaultShiftRepo: Repository<DriverDefaultShift>
  ) {}

  async seedDatabase (pathToFile: string): Promise<void> {
    const csv = fs.readFileSync(pathToFile, 'utf-8')
    const records = csvStringToObjectArray<RawShiftCsvRecord>(csv)

    const branchUuid = await this.seedBranch()

    const drivers: Driver[] = []
    const shifts: DriverDefaultShift[] = []

    for (const record of records) {
      const shift = this.parseDefaultShift(record)
      const driver = this.parseDriver(record, branchUuid, shift.uuid)
      drivers.push(driver)
      shifts.push(shift)
    }

    await this.defaultShiftRepo.upsert(shifts, { conflictPaths: { uuid: true } })
    await this.driverRepository.upsert(drivers, { conflictPaths: { uuid: true } })
  }

  async seedBranch (): Promise<string> {
    const branch = this.branchRepository.create({ uuid: generateUuid('Lommel'), name: 'Lommel' })
    await this.branchRepository.upsert(branch, { conflictPaths: { uuid: true } })
    return branch.uuid
  }

  parseDefaultShift (
    record: RawShiftCsvRecord
  ): DriverDefaultShift {
    return this.defaultShiftRepo.create({
      uuid: generateUuid(`shift-${record.Chauffeur}`),
      licensePlate: `T-TXA-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      from: this.parseTime(record['werktijd van']),
      until: this.parseTime(record.tot),
      timezone: Timezone.EUROPE_BRUSSELS,
      daysOfWeek: [1, 2, 3, 4, 5],
      startLocationUuid: record['Start Uuid'],
      stopLocationUuid: record['Stop Uuid']
    })
  }

  private splitName (fullName: string): { firstName: string, lastName: string } {
    const [firstName, ...lastNameParts] = fullName.split(' ').map((part) => {
      return this.capitalizeFirstLetter(part)
    })
    const lastName = lastNameParts.join(' ')
    return { firstName, lastName }
  }

  parseTime (time: string): Time {
    const dayjsTime = dayjs(`${time}`, 'HH:mm', true)

    if (!dayjsTime.isValid()) {
      throw new Error('Invalid time')
    }

    return new Time(dayjsTime.hour(), dayjsTime.minute(), dayjsTime.second())
  }

  capitalizeFirstLetter (string: string): string {
    if (!string) return string
    return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase()
  }

  private parseDriver (record: RawShiftCsvRecord, branchUuid: string, shiftUuid: string): Driver {
    const { lastName, firstName } = this.splitName(record.Chauffeur)
    const driverUuid = generateUuid(record.Chauffeur)
    return this.driverRepository.create({
      uuid: driverUuid,
      firstName: firstName,
      lastName: lastName,
      branchUuid: branchUuid,
      defaultShiftUuid: shiftUuid
    })
  }
}
