import assert from 'assert'
import { Injectable } from '@nestjs/common'
import dayjs from 'dayjs'
import { Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { combineWithTime } from '../../utils/dates/combine-dayjs-with-time.helper.js'
import { Shift } from '../../apps/resource-management/shift/entities/shift.entity.js'
import { Driver } from '../../apps/resource-management/driver/entities/driver/driver.entity.js'

@Injectable()
export class SeedAvailabilitiesUseCase {
  constructor (
    @InjectRepository(Driver) private readonly driverRepository: Repository<Driver>,
    @InjectRepository(Shift) private readonly shiftRepo: Repository<Shift>
  ) {}

  async seedDriverAvailability (startDate: string, endDate: string): Promise<void> {
    const start = dayjs(startDate)
    const end = dayjs(endDate)

    const drivers = await this.driverRepository.find({
      relations: { defaultShift: true }
    })
    const shifts: Shift[] = []

    for (let date = start; date.isBefore(end) || date.isSame(end, 'day'); date = date.add(1, 'day')) {
      if (date.day() === 6 || date.day() === 0) {
        continue
      }

      for (const driver of drivers) {
        const defaultShift = driver.defaultShift
        assert(defaultShift !== undefined)

        const shift = this.shiftRepo.create({
          licensePlate: defaultShift.licensePlate,
          from: combineWithTime(date, defaultShift.from, defaultShift.timezone),
          until: combineWithTime(date, defaultShift.until, defaultShift.timezone),
          startLocationUuid: defaultShift.startLocationUuid,
          stopLocationUuid: defaultShift.stopLocationUuid,
          driverUuid: driver.uuid,
          branchUuid: driver.branchUuid
        })

        shifts.push(shift)
      }
    }

    await this.shiftRepo.insert(shifts)
  }
}
