import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Shift } from '../../apps/resource-management/shift/entities/shift.entity.js'
import { AcceptedTransportOrder } from '../../apps/order-management/accepted-transport-order/entities/accepted-transport-order.entity.js'
import { Branch } from '../../apps/resource-management/branch/branch.entity.js'
import { DriverDefaultShift } from '../../apps/resource-management/driver-default-shift/entities/driver-default-shift.entity.js'
import { Driver } from '../../apps/resource-management/driver/entities/driver/driver.entity.js'
import { Organization } from '../../apps/order-management/organization/entities/organization.entity.js'
import { CareUser } from '../../apps/order-management/care-user/entities/care-user.entity.js'
import { ContractType } from '../../apps/order-management/contract-type/entities/contract-type.entity.js'
import { Location } from '../location/entities/location.entity.js'
import { MaxTimeInVehicleFormula } from '../../apps/order-management/max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { InvoiceDetail } from '../../apps/order-management/invoice-detail/invoice-detail.entity.js'
import { Department } from '../../apps/order-management/department/entities/department.entity.js'
import { Contract } from '../../apps/order-management/contract/entities/contract.entity.js'
import { TypesenseModule } from '../typesense/typesense.module.js'
import { SeedContractTypesUseCase } from './seed-contract-types.use-case.js'
import { SeedOrdersUseCase } from './seed-orders.use-case.js'
import { SeedAvailabilitiesUseCase } from './seed-availabilities.use-case.js'
import { SeedDriversAndDefaultShiftsUseCase } from './seed-drivers-and-default-shifts.use-case.js'
import { SeedLocationsUseCase } from './seed-locations.use-case.js'
import { SeedMaxTimeInVehicleFormulaUseCase } from './seed-max-time-in-vehicle-formula.use-case.js'
import { DuplicateATOsAcrossDateRangeUseCase } from './duplicate-atos-across-date-range.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Branch,
      DriverDefaultShift,
      Driver,
      Shift,
      ContractType,
      Organization,
      CareUser,
      AcceptedTransportOrder,
      Location,
      MaxTimeInVehicleFormula,
      InvoiceDetail,
      Department,
      Contract
    ]),
    TypesenseModule
  ],
  providers: [
    SeedDriversAndDefaultShiftsUseCase,
    SeedAvailabilitiesUseCase,
    SeedOrdersUseCase,
    SeedContractTypesUseCase,
    DuplicateATOsAcrossDateRangeUseCase,
    SeedLocationsUseCase,
    SeedMaxTimeInVehicleFormulaUseCase
  ],
  exports: [
    SeedDriversAndDefaultShiftsUseCase,
    SeedAvailabilitiesUseCase,
    SeedLocationsUseCase,
    SeedContractTypesUseCase,
    DuplicateATOsAcrossDateRangeUseCase,
    SeedMaxTimeInVehicleFormulaUseCase
  ]
})
export class DatabaseSeederModule {
}
