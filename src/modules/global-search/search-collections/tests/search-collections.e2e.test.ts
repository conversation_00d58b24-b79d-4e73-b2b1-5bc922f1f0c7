import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { stringify } from 'qs'
import { EndToEndTestSetup } from '../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { TestUser } from '../../../../app/users/tests/setup-user.type.js'
import { TypesenseCollectionName } from '../../../typesense/collections/typesense-collection-name.enum.js'
import { TypesenseCollectionService } from '../../../typesense/services/typesense-collection.service.js'
import { SearchCollectionsQueryBuilder } from '../query/search-collections.query-builder.js'
import { DriverEntityBuilder } from '../../../../apps/resource-management/driver/entities/driver/driver.entity.builder.js'
import { Driver } from '../../../../apps/resource-management/driver/entities/driver/driver.entity.js'

describe('Search collections e2e test', () => {
  let setup: EndToEndTestSetup
  let adminUser: TestUser
  let typesense: TypesenseCollectionService
  let driver1: Driver
  let driver2: Driver

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    adminUser = await setup.authContext.getAdminUser()
    typesense = setup.testModule.get(TypesenseCollectionService, { strict: false })

    driver1 = new DriverEntityBuilder()
      .withFirstName('Test')
      .withLastName('1')
      .build()

    driver2 = new DriverEntityBuilder()
      .withFirstName('Frank')
      .withLastName('De Tester')
      .build()

    const driver3 = new DriverEntityBuilder()
      .withFirstName('User')
      .withLastName('3')
      .build()

    await typesense.importManually(
      TypesenseCollectionName.DRIVER,
      [driver1, driver2, driver3]
    )
  })

  after(async () => {
    await setup.teardown()
  })

  it('returns the searched collection ordered on text score', async () => {
    const query = new SearchCollectionsQueryBuilder()
      .withSearch('Test')
      .withFilterOn([TypesenseCollectionName.DRIVER])
      .build()

    const response = await request(setup.app.getHttpServer())
      .get('/search')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(2)
    expect(response.body.items[0].entity.uuid).toEqual(driver1.uuid)
    expect(response.body.items[0].collection).toEqual(TypesenseCollectionName.DRIVER)
    expect(response.body.items[1].entity.uuid).toEqual(driver2.uuid)
    expect(response.body.items[1].collection).toEqual(TypesenseCollectionName.DRIVER)
  })

  it('returns search results of all collections when not filtered', async () => {
    const query = new SearchCollectionsQueryBuilder()
      .withSearch('Test')
      .build()

    const response = await request(setup.app.getHttpServer())
      .get('/search')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(2)
    expect(response.body.items[0].entity.uuid).toEqual(driver1.uuid)
    expect(response.body.items[0].collection).toEqual(TypesenseCollectionName.DRIVER)
    expect(response.body.items[1].entity.uuid).toEqual(driver2.uuid)
    expect(response.body.items[1].collection).toEqual(TypesenseCollectionName.DRIVER)
  })
})
