import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddCareUserAddress1751872263514 implements MigrationInterface {
  name = 'AddCareUserAddress1751872263514'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE "care_user_address" ("uuid" uuid NOT NULL DEFAULT uuid_generate_v4(), "address" jsonb NOT NULL, "care_user_uuid" uuid NOT NULL, "stop_duration" integer NOT NULL DEFAULT '0', "action_duration" integer NOT NULL DEFAULT '0', "remarks_for_driver" character varying, "remarks_for_planner" character varying, CONSTRAINT "PK_1fa02e7d0dce0b327ca620003ae" PRIMARY KEY ("uuid"))`)
    await queryRunner.query(`ALTER TABLE "care_user_address" ADD CONSTRAINT "FK_c278fda055f76742fb917d0302e" FOREIGN KEY ("care_user_uuid") REFERENCES "care_user"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "care_user_address" DROP CONSTRAINT "FK_c278fda055f76742fb917d0302e"`)
    await queryRunner.query(`DROP TABLE "care_user_address"`)
  }
}
