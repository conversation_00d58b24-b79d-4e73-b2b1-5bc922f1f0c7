import { MigrationInterface, QueryRunner } from 'typeorm'

export class DriverAvailabilityUseDaterangecolumn1751281289911 implements MigrationInterface {
  name = 'DriverAvailabilityUseDaterangecolumn1751281289911'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "driver_availability" DROP COLUMN "start_date"`)
    await queryRunner.query(`ALTER TABLE "driver_availability" DROP COLUMN "end_date"`)
    await queryRunner.query(`ALTER TABLE "driver_availability" DROP COLUMN "start_time"`)
    await queryRunner.query(`ALTER TABLE "driver_availability" DROP COLUMN "end_time"`)
    await queryRunner.query(`ALTER TABLE "driver_availability" DROP COLUMN "is_recurring"`)
    await queryRunner.query(`ALTER TABLE "driver_availability" DROP COLUMN "weeks_period"`)
    await queryRunner.query(`ALTER TABLE "driver_availability" ADD "period" smallint NOT NULL DEFAULT '1'`)
    await queryRunner.query(`ALTER TABLE "driver_availability" ADD "slots" jsonb NOT NULL DEFAULT '[]'`)
    await queryRunner.query(`ALTER TABLE "driver_availability" ADD "weekly_hours_available" smallint NOT NULL DEFAULT '0'`)
    await queryRunner.query(`ALTER TABLE "driver_availability" ADD "daterange" daterange NOT NULL`)
    await queryRunner.query(`CREATE INDEX "driver_availability_daterange_idx" ON "driver_availability" USING GiST ("daterange") `)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."driver_availability_daterange_idx"`)
    await queryRunner.query(`ALTER TABLE "driver_availability" DROP COLUMN "daterange"`)
    await queryRunner.query(`ALTER TABLE "driver_availability" DROP COLUMN "weekly_hours_available"`)
    await queryRunner.query(`ALTER TABLE "driver_availability" DROP COLUMN "slots"`)
    await queryRunner.query(`ALTER TABLE "driver_availability" DROP COLUMN "period"`)
    await queryRunner.query(`ALTER TABLE "driver_availability" ADD "weeks_period" smallint`)
    await queryRunner.query(`ALTER TABLE "driver_availability" ADD "is_recurring" boolean NOT NULL DEFAULT false`)
    await queryRunner.query(`ALTER TABLE "driver_availability" ADD "end_time" TIME NOT NULL`)
    await queryRunner.query(`ALTER TABLE "driver_availability" ADD "start_time" TIME NOT NULL`)
    await queryRunner.query(`ALTER TABLE "driver_availability" ADD "end_date" date`)
    await queryRunner.query(`ALTER TABLE "driver_availability" ADD "start_date" date NOT NULL`)
  }
}
